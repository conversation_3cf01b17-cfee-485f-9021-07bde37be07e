# ESP32S3 Linux嵌入式板测试系统 Makefile

# 默认串口设备 (可通过命令行参数覆盖)
PORT ?= /dev/ttyUSB0
BAUDRATE ?= 115200

# 项目信息
PROJECT_NAME = GS068-01_test
VERSION = v1.0

# 颜色定义
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[0;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help build flash monitor clean test test-activation test-gpio test-debug test-usb info setup

# 默认目标
all: build

help:
	@echo "$(BLUE)ESP32S3 Linux嵌入式板测试系统 $(VERSION)$(NC)"
	@echo ""
	@echo "$(GREEN)可用命令:$(NC)"
	@echo "  $(YELLOW)build$(NC)          - 编译项目"
	@echo "  $(YELLOW)flash$(NC)          - 烧录到ESP32S3"
	@echo "  $(YELLOW)monitor$(NC)        - 监控串口输出"
	@echo "  $(YELLOW)flash-monitor$(NC)  - 烧录并监控"
	@echo "  $(YELLOW)clean$(NC)          - 清理编译文件"
	@echo "  $(YELLOW)menuconfig$(NC)     - 配置项目"
	@echo ""
	@echo "$(GREEN)测试命令:$(NC)"
	@echo "  $(YELLOW)test$(NC)           - 运行所有自动化测试"
	@echo "  $(YELLOW)test-activation$(NC) - 测试系统激活"
	@echo "  $(YELLOW)test-gpio$(NC)      - 测试GPIO状态查询"
	@echo "  $(YELLOW)test-debug$(NC)     - 测试Debug登录"
	@echo "  $(YELLOW)test-usb$(NC)       - 测试USB更新功能"
	@echo ""
	@echo "$(GREEN)调试命令:$(NC)"
	@echo "  $(YELLOW)debug-test$(NC)     - 运行调试测试（基本功能）"
	@echo "  $(YELLOW)debug-monitor$(NC)  - 监控系统日志"
	@echo ""
	@echo "$(GREEN)工具命令:$(NC)"
	@echo "  $(YELLOW)info$(NC)           - 显示项目信息"
	@echo "  $(YELLOW)setup$(NC)          - 设置开发环境"
	@echo "  $(YELLOW)size$(NC)           - 显示固件大小信息"
	@echo ""
	@echo "$(GREEN)参数:$(NC)"
	@echo "  PORT=$(PORT)     - 串口设备路径"
	@echo "  BAUDRATE=$(BAUDRATE)   - 串口波特率"
	@echo ""
	@echo "$(GREEN)示例:$(NC)"
	@echo "  make build"
	@echo "  make flash PORT=/dev/ttyUSB1"
	@echo "  make test PORT=COM3"

build:
	@echo "$(GREEN)编译项目...$(NC)"
	idf.py build

flash:
	@echo "$(GREEN)烧录到 $(PORT)...$(NC)"
	idf.py -p $(PORT) flash

monitor:
	@echo "$(GREEN)监控串口 $(PORT) (波特率: $(BAUDRATE))...$(NC)"
	@echo "$(YELLOW)按 Ctrl+] 退出监控$(NC)"
	idf.py -p $(PORT) monitor

flash-monitor: flash monitor

clean:
	@echo "$(GREEN)清理编译文件...$(NC)"
	idf.py fullclean

menuconfig:
	@echo "$(GREEN)配置项目...$(NC)"
	idf.py menuconfig

size:
	@echo "$(GREEN)固件大小信息:$(NC)"
	idf.py size

# 测试相关命令
test:
	@echo "$(GREEN)运行所有自动化测试...$(NC)"
	@if [ ! -f test_script.py ]; then \
		echo "$(RED)错误: test_script.py 文件不存在$(NC)"; \
		exit 1; \
	fi
	python3 test_script.py $(PORT) --baudrate $(BAUDRATE)

test-activation:
	@echo "$(GREEN)测试系统激活...$(NC)"
	python3 test_script.py $(PORT) --baudrate $(BAUDRATE) --test activation

test-gpio:
	@echo "$(GREEN)测试GPIO状态查询...$(NC)"
	python3 test_script.py $(PORT) --baudrate $(BAUDRATE) --test gpio

test-debug:
	@echo "$(GREEN)测试Debug登录...$(NC)"
	python3 test_script.py $(PORT) --baudrate $(BAUDRATE) --test debug

test-usb:
	@echo "$(GREEN)测试USB更新功能...$(NC)"
	python3 test_script.py $(PORT) --baudrate $(BAUDRATE) --test usb

# 调试相关命令
debug-test:
	@echo "$(GREEN)运行调试测试...$(NC)"
	python3 debug_test.py $(PORT) --baudrate $(BAUDRATE)

debug-monitor:
	@echo "$(GREEN)监控系统日志...$(NC)"
	python3 debug_test.py $(PORT) --baudrate $(BAUDRATE) --monitor --duration 60

# 信息显示
info:
	@echo "$(BLUE)项目信息:$(NC)"
	@echo "  项目名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  目标芯片: ESP32S3"
	@echo "  开发框架: ESP-IDF"
	@echo ""
	@echo "$(BLUE)硬件配置:$(NC)"
	@echo "  主控制串口: UART0 (GPIO4/5)"
	@echo "  Debug串口: UART1 (GPIO6/7)"
	@echo "  透传串口1: UART2 (GPIO15/16)"
	@echo "  透传串口2: 软件UART (GPIO17/18)"
	@echo "  GPIO监控: GPIO35-42 (8个引脚)"
	@echo "  USB-OTG: GPIO19/20"
	@echo ""
	@echo "$(BLUE)AT指令:$(NC)"
	@echo "  <AT_STAR>                    - 系统激活"
	@echo "  <AT_TEST_DEBUG>              - Debug登录测试"
	@echo "  <AT_GET_GPIO_STA>            - GPIO状态查询"
	@echo "  <AT_TEST_USB_UPDATE>         - USB更新测试"
	@echo "  <AT_GET_TEST_USB_UPDATE_STA> - USB更新状态查询"

# 开发环境设置
setup:
	@echo "$(GREEN)检查开发环境...$(NC)"
	@echo "$(YELLOW)检查ESP-IDF...$(NC)"
	@if [ -z "$$IDF_PATH" ]; then \
		echo "$(RED)错误: ESP-IDF环境未设置$(NC)"; \
		echo "请运行: . $$HOME/esp/esp-idf/export.sh"; \
		exit 1; \
	else \
		echo "$(GREEN)ESP-IDF路径: $$IDF_PATH$(NC)"; \
	fi
	@echo "$(YELLOW)检查Python依赖...$(NC)"
	@python3 -c "import serial" 2>/dev/null || { \
		echo "$(RED)错误: pyserial未安装$(NC)"; \
		echo "请运行: pip3 install pyserial"; \
		exit 1; \
	}
	@echo "$(GREEN)开发环境检查完成$(NC)"

# 快速开发流程
dev: build flash-monitor

# 完整测试流程
full-test: build flash
	@echo "$(YELLOW)等待设备启动...$(NC)"
	@sleep 3
	@$(MAKE) test

# 检查串口设备
check-port:
	@echo "$(GREEN)检查串口设备 $(PORT)...$(NC)"
	@if [ ! -e $(PORT) ]; then \
		echo "$(RED)错误: 串口设备 $(PORT) 不存在$(NC)"; \
		echo "$(YELLOW)可用串口设备:$(NC)"; \
		ls /dev/tty* 2>/dev/null | grep -E "(USB|ACM)" || echo "未找到串口设备"; \
		exit 1; \
	else \
		echo "$(GREEN)串口设备 $(PORT) 存在$(NC)"; \
	fi

# 显示日志
logs:
	@echo "$(GREEN)显示最近的编译日志...$(NC)"
	@if [ -f build/log/idf_py_stdout_output_* ]; then \
		tail -n 50 build/log/idf_py_stdout_output_*; \
	else \
		echo "$(YELLOW)没有找到编译日志$(NC)"; \
	fi

# 备份项目
backup:
	@echo "$(GREEN)备份项目...$(NC)"
	@DATE=$$(date +%Y%m%d_%H%M%S); \
	tar -czf $(PROJECT_NAME)_backup_$$DATE.tar.gz \
		--exclude=build \
		--exclude=.git \
		--exclude="*.tar.gz" \
		. && \
	echo "$(GREEN)备份完成: $(PROJECT_NAME)_backup_$$DATE.tar.gz$(NC)"
