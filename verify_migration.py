#!/usr/bin/env python3
"""
验证Dedicated GPIO软串口迁移的脚本
检查代码修改是否完整和正确
"""

import os
import re
import sys

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - 文件不存在")
        return False

def check_file_contains(filepath, pattern, description):
    """检查文件是否包含特定内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if re.search(pattern, content, re.MULTILINE):
                print(f"✅ {description}")
                return True
            else:
                print(f"❌ {description} - 未找到匹配内容")
                return False
    except FileNotFoundError:
        print(f"❌ {description} - 文件不存在: {filepath}")
        return False
    except Exception as e:
        print(f"❌ {description} - 读取文件错误: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证Dedicated GPIO软串口迁移...")
    print("=" * 60)
    
    success_count = 0
    total_checks = 0
    
    # 检查文件存在性
    files_to_check = [
        ("main/system_config.h", "系统配置头文件"),
        ("main/uart_passthrough.h", "串口透传头文件"),
        ("main/uart_passthrough.c", "串口透传实现文件"),
        ("main/soft_uart_test.h", "软串口测试头文件"),
        ("main/soft_uart_test.c", "软串口测试实现文件"),
        ("main/soft_uart_integration_example.h", "集成示例头文件"),
        ("main/soft_uart_integration_example.c", "集成示例实现文件"),
        ("main/CMakeLists.txt", "构建配置文件"),
        ("DEDICATED_GPIO_SOFT_UART_README.md", "使用说明文档"),
        ("MIGRATION_CHECKLIST.md", "迁移检查清单")
    ]
    
    print("\n📁 文件存在性检查:")
    for filepath, description in files_to_check:
        if check_file_exists(filepath, description):
            success_count += 1
        total_checks += 1
    
    # 检查关键代码内容
    print("\n🔧 关键代码内容检查:")
    
    code_checks = [
        ("main/system_config.h", r"#include.*dedic_gpio\.h", "包含Dedicated GPIO头文件"),
        ("main/system_config.h", r"dedic_gpio_bundle_handle_t.*tx_bundle", "定义TX GPIO束"),
        ("main/system_config.h", r"dedic_gpio_bundle_handle_t.*rx_bundle", "定义RX GPIO束"),
        ("main/uart_passthrough.c", r"dedic_gpio_new_bundle", "使用GPIO束创建函数"),
        ("main/uart_passthrough.c", r"dedic_gpio_cpu_ll_write_mask", "使用底层GPIO写函数"),
        ("main/uart_passthrough.c", r"dedic_gpio_cpu_ll_read_in", "使用底层GPIO读函数"),
        ("main/uart_passthrough.c", r"delay_us_precise", "使用精确延时函数"),
        ("main/uart_passthrough.c", r"soft_uart_send_byte", "定义发送字节函数"),
        ("main/uart_passthrough.c", r"soft_uart_receive_byte", "定义接收字节函数"),
        ("main/CMakeLists.txt", r"esp_driver_gpio", "包含GPIO驱动依赖"),
        ("main/CMakeLists.txt", r"esp_timer", "包含定时器依赖")
    ]
    
    for filepath, pattern, description in code_checks:
        if check_file_contains(filepath, pattern, description):
            success_count += 1
        total_checks += 1
    
    # 检查函数签名
    print("\n🔍 函数接口检查:")
    
    function_checks = [
        ("main/uart_passthrough.h", r"uint32_t uart_passthrough_get_soft_uart_errors\(void\)", "错误统计函数声明"),
        ("main/uart_passthrough.c", r"uint32_t uart_passthrough_get_soft_uart_errors\(void\)", "错误统计函数实现"),
        ("main/soft_uart_test.h", r"void run_all_soft_uart_tests\(void\)", "测试套件函数声明"),
        ("main/soft_uart_test.c", r"void run_all_soft_uart_tests\(void\)", "测试套件函数实现")
    ]
    
    for filepath, pattern, description in function_checks:
        if check_file_contains(filepath, pattern, description):
            success_count += 1
        total_checks += 1
    
    # 检查配置和宏定义
    print("\n⚙️ 配置检查:")
    
    config_checks = [
        ("main/system_config.h", r"#define.*ENABLE_SOFTWARE_UART", "软串口使能宏"),
        ("main/uart_passthrough.c", r"IRAM_ATTR", "IRAM属性使用"),
        ("main/uart_passthrough.c", r"portMUX_TYPE.*mux", "临界区保护")
    ]
    
    for filepath, pattern, description in config_checks:
        if check_file_contains(filepath, pattern, description):
            success_count += 1
        total_checks += 1
    
    # 输出结果
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("🎉 所有检查项目都通过！迁移看起来是成功的。")
        print("\n📋 下一步建议:")
        print("1. 运行 'idf.py build' 检查编译")
        print("2. 烧录并测试基本功能")
        print("3. 运行测试套件验证性能")
        print("4. 进行长时间稳定性测试")
        return 0
    else:
        failed_count = total_checks - success_count
        print(f"⚠️  有 {failed_count} 项检查未通过，请检查上述错误信息。")
        print("\n🔧 故障排除建议:")
        print("1. 检查文件路径是否正确")
        print("2. 确认代码修改是否完整")
        print("3. 验证语法是否正确")
        print("4. 查看详细错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
