# 软件UART接收功能测试

## 概述

这个测试套件专门用于测试被注释掉的软件UART接收代码。测试覆盖了以下关键功能：

1. **基本字节接收** - 测试不同字节值的正确接收
2. **起始位检测** - 验证假起始位的正确处理
3. **位时序** - 确保时序准确性
4. **透传功能** - 验证接收到的数据被正确转发到发送队列
5. **边界条件** - 测试各种边界字节值
6. **队列管理** - 测试队列满等异常情况

## 被测试的代码

测试的是 `uart_passthrough.c` 文件中第254-291行被注释掉的软件UART接收逻辑：

```c
// 等待起始位 (下降沿)
while (gpio_get_level(g_soft_uart.rx_pin) == 1) {
    vTaskDelay(pdMS_TO_TICKS(1));
}

// 临界区保护，确保位时序准确
taskENTER_CRITICAL(&soft_uart_mux);

// 等待半个位时间，到达起始位中心
delay_us_optimized(SOFT_UART_BIT_TIME_US / 2);

// 确认起始位
if (gpio_get_level(g_soft_uart.rx_pin) != 0) {
    taskEXIT_CRITICAL(&soft_uart_mux);
    continue; // 假起始位，继续等待
}

uint8_t rx_byte = 0;

// 接收8个数据位
for (int i = 0; i < 8; i++) {
    delay_us_optimized(SOFT_UART_BIT_TIME_US);
    if (gpio_get_level(g_soft_uart.rx_pin)) {
        rx_byte |= (1 << i);
    }
}

// 等待停止位
delay_us_optimized(SOFT_UART_BIT_TIME_US);

taskEXIT_CRITICAL(&soft_uart_mux);

// 将接收到的数据放入接收队列
if (xQueueSend(g_soft_uart.rx_queue, &rx_byte, 0) == pdTRUE) {
    uart_stats[UART_NUM_MAX].rx_bytes++;

    // 立即回传数据（透传功能）
    xQueueSend(g_soft_uart.tx_queue, &rx_byte, 0);
}
```

## 测试用例说明

### 1. test_receive_byte_0x55
测试接收字节 0x55 (01010101)，这是一个交替位模式，能很好地测试位采样的准确性。

### 2. test_receive_byte_0xAA  
测试接收字节 0xAA (10101010)，与0x55相反的交替位模式。

### 3. test_receive_byte_0x00
测试接收全0字节，验证低电平位的正确处理。

### 4. test_receive_byte_0xFF
测试接收全1字节，验证高电平位的正确处理。

### 5. test_false_start_bit_detection
测试假起始位检测功能，确保噪声不会导致错误的数据接收。

### 6. test_receive_multiple_bytes
测试连续接收多个字节，模拟实际通信场景。

### 7. test_bit_timing_accuracy
验证位时序的准确性，确保延时函数被正确调用。

### 8. test_queue_full_scenario
测试队列满的异常情况，确保系统稳定性。

### 9. test_boundary_byte_values
测试各种边界字节值，包括单位模式和其反码。

### 10. test_passthrough_functionality
专门测试透传功能，确保接收的数据被正确转发。

## 模拟机制

测试使用以下模拟机制：

- **GPIO模拟**: `mock_gpio_get_level()` 函数模拟GPIO电平读取
- **延时模拟**: `mock_delay_us_optimized()` 函数模拟微秒级延时
- **队列模拟**: `mock_xQueueSend()` 函数模拟FreeRTOS队列操作
- **电平序列**: 通过预设的电平序列模拟完整的UART字节传输

## 运行测试

1. 确保ESP-IDF环境已正确配置
2. 在项目根目录运行：
   ```bash
   idf.py build
   idf.py flash monitor
   ```

## 预期结果

所有测试用例都应该通过，输出类似：
```
Unity test run 1 of 1
test_receive_byte_0x55:PASS
test_receive_byte_0xAA:PASS
test_receive_byte_0x00:PASS
test_receive_byte_0xFF:PASS
test_false_start_bit_detection:PASS
test_receive_multiple_bytes:PASS
test_bit_timing_accuracy:PASS
test_queue_full_scenario:PASS
test_boundary_byte_values:PASS
test_passthrough_functionality:PASS

10 Tests 0 Failures 0 Ignored
OK
```

## 注意事项

1. 这些测试是单元测试，不涉及实际的GPIO硬件操作
2. 测试专注于逻辑正确性，而不是实际的时序精度
3. 在实际硬件上运行时，可能需要调整时序参数
4. 测试假设115200波特率的UART通信

## 扩展测试

可以考虑添加以下测试：

1. 不同波特率的测试
2. 噪声干扰的测试
3. 长时间运行的稳定性测试
4. 实际GPIO硬件的集成测试
