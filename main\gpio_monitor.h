#ifndef GPIO_MONITOR_H
#define GPIO_MONITOR_H

#include "system_config.h"

/**
 * @brief 初始化GPIO监控模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t gpio_monitor_init(void);

/**
 * @brief 获取当前GPIO状态
 * @return 8位GPIO状态，GPIO35对应最高位
 */
uint8_t gpio_monitor_get_status(void);

/**
 * @brief 获取指定GPIO引脚的状态
 * @param pin_index GPIO引脚索引 (0-7)
 * @return GPIO电平状态 (0或1)，-1表示无效索引
 */
int gpio_monitor_get_pin_status(int pin_index);

/**
 * @brief GPIO监控任务
 * @param pvParameters 任务参数
 */
void gpio_monitor_task(void* pvParameters);

/**
 * @brief 启动GPIO监控任务
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t gpio_monitor_start_task(void);

/**
 * @brief 停止GPIO监控任务
 */
void gpio_monitor_stop_task(void);

/**
 * @brief 获取GPIO状态变化统计信息
 * @param pin_index GPIO引脚索引 (0-7)
 * @param change_count 输出参数：状态变化次数
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t gpio_monitor_get_change_count(int pin_index, uint32_t* change_count);

/**
 * @brief 重置GPIO状态变化统计
 */
void gpio_monitor_reset_change_count(void);

/**
 * @brief 去初始化GPIO监控模块
 */
void gpio_monitor_deinit(void);

#endif // GPIO_MONITOR_H
