#!/usr/bin/env python3
"""
ESP32S3 调试测试脚本
用于测试修复栈溢出问题后的基本功能
"""

import serial
import time
import sys
import argparse

def test_basic_functionality(port, baudrate=115200):
    """测试基本功能"""
    try:
        # 连接串口
        ser = serial.Serial(port, baudrate, timeout=5)
        print(f"已连接到 {port}，波特率 {baudrate}")
        
        # 等待系统启动
        print("等待系统启动...")
        time.sleep(3)
        
        # 清空缓冲区
        ser.reset_input_buffer()
        
        # 测试系统激活
        print("\n=== 测试系统激活 ===")
        ser.write(b"<AT_STAR>")
        ser.flush()
        print("发送: <AT_STAR>")
        
        # 等待响应
        start_time = time.time()
        response = ""
        while time.time() - start_time < 5:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                response += data
                if "<AT_STAR_OK>" in response:
                    print("✓ 收到响应: <AT_STAR_OK>")
                    break
            time.sleep(0.1)
        else:
            print("✗ 未收到期望响应")
            return False
        
        # 测试GPIO状态查询
        print("\n=== 测试GPIO状态查询 ===")
        ser.reset_input_buffer()
        ser.write(b"<AT_GET_GPIO_STA>")
        ser.flush()
        print("发送: <AT_GET_GPIO_STA>")
        
        # 等待响应
        start_time = time.time()
        response = ""
        while time.time() - start_time < 5:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                response += data
                if "<AT_GET_GPIO_" in response:
                    print(f"✓ 收到GPIO状态响应: {response.strip()}")
                    break
            time.sleep(0.1)
        else:
            print("✗ 未收到GPIO状态响应")
            return False
        
        print("\n✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        if 'ser' in locals():
            ser.close()

def monitor_system_logs(port, baudrate=115200, duration=30):
    """监控系统日志"""
    try:
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"监控系统日志 {duration} 秒...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                print(data, end='')
            time.sleep(0.1)
        
        print(f"\n监控完成")
        
    except Exception as e:
        print(f"监控失败: {e}")
    finally:
        if 'ser' in locals():
            ser.close()

def main():
    parser = argparse.ArgumentParser(description='ESP32S3调试测试脚本')
    parser.add_argument('port', help='串口设备路径')
    parser.add_argument('-b', '--baudrate', type=int, default=115200, help='波特率')
    parser.add_argument('-m', '--monitor', action='store_true', help='监控模式')
    parser.add_argument('-d', '--duration', type=int, default=30, help='监控时长（秒）')
    
    args = parser.parse_args()
    
    if args.monitor:
        monitor_system_logs(args.port, args.baudrate, args.duration)
    else:
        success = test_basic_functionality(args.port, args.baudrate)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
