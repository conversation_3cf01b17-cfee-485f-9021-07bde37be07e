# ESP32S3 Linux嵌入式板测试系统

基于ESP32S3开发的Linux嵌入式板测试程序，实现了完整的硬件连接拓扑、状态机管理、AT指令处理、多线程协调等功能。

## 🚀 项目特性

- **模块化架构**：采用模块化设计，便于维护和扩展
- **状态机管理**：IDLE和TESTING两个主要状态，支持状态转换
- **AT指令处理**：完整的AT指令解析和处理系统
- **多线程协调**：GPIO监控、串口透传、USB监控等多任务并行
- **错误处理**：完善的超时处理和错误恢复机制
- **实时监控**：GPIO状态实时监控和变化检测
- **串口透传**：支持硬件和软件UART的数据透传
- **USB-OTG支持**：U盘文件系统读取和文件检测
- **U-Boot操作**：封装的U-Boot命令行操作流程

## 📋 硬件要求

- ESP32S3开发板
- Linux嵌入式板（支持Debug串口）
- USB-OTG连接线和U盘
- 杜邦线用于GPIO连接

## 🔌 硬件连接

### ESP32S3引脚分配

| 功能 | ESP32S3引脚 | 连接目标 | 说明 |
|------|-------------|----------|------|
| 主控制串口TX | GPIO4 | Linux板测试程序RX | 接收AT指令 |
| 主控制串口RX | GPIO5 | Linux板测试程序TX | 发送AT响应 |
| Debug串口TX | GPIO6 | Linux板Debug串口RX | 控制Linux板 |
| Debug串口RX | GPIO7 | Linux板Debug串口TX | 接收Linux板输出 |
| 透传串口1 TX | GPIO15 | 外部设备RX | 数据透传 |
| 透传串口1 RX | GPIO16 | 外部设备TX | 数据透传 |
| 透传串口2 TX | GPIO17 | 外部设备RX | 软件UART透传 |
| 透传串口2 RX | GPIO18 | 外部设备TX | 软件UART透传 |
| GPIO监控 | GPIO35-42 | Linux板GPIO输出 | 8个GPIO状态监控 |
| USB-OTG D+ | GPIO19 | USB D+ | U盘连接 |
| USB-OTG D- | GPIO20 | USB D- | U盘连接 |

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 安装ESP-IDF (如果尚未安装)
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh
. ./export.sh

# 安装Python依赖
pip3 install pyserial
```

### 2. 编译和烧录

```bash
# 克隆项目
git clone <repository-url>
cd GS068-01_test

# 编译项目
make build

# 烧录到ESP32S3 (替换为实际串口)
make flash PORT=/dev/ttyUSB0

# 监控串口输出
make monitor PORT=/dev/ttyUSB0
```

### 3. 系统测试

```bash
# 运行自动化测试
make test PORT=/dev/ttyUSB0

# 或者运行单项测试
make test-activation PORT=/dev/ttyUSB0
make test-gpio PORT=/dev/ttyUSB0
make test-debug PORT=/dev/ttyUSB0
make test-usb PORT=/dev/ttyUSB0
```

## 📡 AT指令接口

### 系统激活
```
发送: <AT_STAR>
响应: <AT_STAR_OK>
功能: 激活系统，从IDLE状态切换到TESTING状态
```

### GPIO状态查询
```
发送: <AT_GET_GPIO_STA>
响应: <AT_GET_GPIO_XXXXXXXX>
功能: 获取GPIO35-42的8位二进制状态
格式: X为0或1，GPIO35对应最高位
```

### Debug登录测试
```
发送: <AT_TEST_DEBUG>
响应: <AT_TEST_DEBUG_OK> 或 <AT_TEST_DEBUG_ERROR>
功能: 测试Linux板Debug串口登录功能
超时: 30秒
```

### USB更新测试
```
发送: <AT_TEST_USB_UPDATE>
功能: 启动USB更新测试流程

查询: <AT_GET_TEST_USB_UPDATE_STA>
响应: <AT_GET_TEST_USB_UPDATE_OK> 或 <AT_GET_TEST_USB_UPDATE_ERROR>
功能: 查询USB更新测试结果
```

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AT指令处理    │    │    状态机管理   │    │   错误处理系统  │
│   (主线程)      │◄──►│   (IDLE/TESTING) │◄──►│   (监控/恢复)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GPIO监控线程  │    │  串口透传线程   │    │   USB监控线程   │
│  (实时状态读取) │    │  (数据转发)     │    │  (文件检测)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
GS068-01_test/
├── main/
│   ├── system_config.h      # 系统配置和数据结构
│   ├── main.c              # 主程序入口
│   ├── state_machine.h/c   # 状态机模块
│   ├── at_commands.h/c     # AT指令处理
│   ├── gpio_monitor.h/c    # GPIO监控
│   ├── uart_passthrough.h/c # 串口透传
│   ├── usb_otg.h/c         # USB-OTG模块
│   ├── uboot_operations.h/c # U-Boot操作封装
│   └── error_handler.h/c   # 错误处理系统
├── CMakeLists.txt          # 项目配置
├── Makefile               # 编译和测试脚本
├── test_script.py         # 自动化测试脚本
├── README.md              # 项目说明
├── README_SYSTEM_DESIGN.md # 系统设计文档
└── USAGE_GUIDE.md         # 使用指南
```

## 🔧 配置选项

### 修改GPIO引脚
编辑 `main/system_config.h`：
```c
static const gpio_num_t gpio_monitor_pins[GPIO_MONITOR_COUNT] = {
    GPIO_NUM_35, GPIO_NUM_36, GPIO_NUM_37, GPIO_NUM_38,
    GPIO_NUM_39, GPIO_NUM_40, GPIO_NUM_41, GPIO_NUM_42
};
```

### 修改超时时间
```c
#define AT_COMMAND_TIMEOUT          30000   // 30秒
#define DEBUG_LOGIN_TIMEOUT         30000   // 30秒
#define UBOOT_COMMAND_TIMEOUT       10000   // 10秒
```

## 📊 系统监控

系统提供实时状态监控：

```
I (12345) MAIN: System Status - State: TESTING, GPIO: 0xA5, USB: 0
I (12346) GPIO_MONITOR: GPIO35 changed to 1 (change count: 5)
I (12347) USB_OTG: USB device connected (simulated)
```

## 🐛 故障排除

### 常见问题

1. **编译错误**
   - 确保ESP-IDF环境正确设置
   - 检查ESP-IDF版本兼容性

2. **串口连接问题**
   - 检查串口设备路径
   - 确认波特率设置正确
   - 检查串口权限

3. **GPIO状态异常**
   - 检查硬件连接
   - 确认Linux板GPIO输出正常

4. **USB功能问题**
   - 当前USB-OTG为简化实现
   - 需要根据实际需求完善USB Host功能

## 📚 文档

- [系统设计文档](README_SYSTEM_DESIGN.md) - 详细的系统架构和设计说明
- [使用指南](USAGE_GUIDE.md) - 完整的使用说明和示例
- [API参考](main/) - 各模块的API文档

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**版本**: GS068-01_v1.0
**目标芯片**: ESP32S3
**开发框架**: ESP-IDF
**最后更新**: 2024年
