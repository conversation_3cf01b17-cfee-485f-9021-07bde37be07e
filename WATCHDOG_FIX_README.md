# ESP32S3 任务看门狗超时错误修复方案

## 问题描述

系统出现任务看门狗超时错误，错误信息显示：
```
E (15713) task_wdt: Task watchdog got triggered. The following tasks/users did not reset the watchdog in time: 
E (15713) task_wdt:  - IDLE0 (CPU 0) 
E (15713) task_wdt: CPU 0: soft_uart_rx 
```

## 根本原因分析

### 1. 主要问题
- **忙等待循环阻塞CPU**：`soft_uart_receive_byte`函数中的起始位等待循环会持续运行100ms，完全占用CPU
- **任务优先级过低**：UART任务优先级设置为1（最低），容易被其他任务抢占
- **缺少CPU让出机制**：长时间运行的循环中没有调用让出CPU的函数
- **看门狗超时**：5秒的看门狗超时时间内，IDLE任务无法运行

### 2. 问题代码位置
- `uart_passthrough.c:91` - `soft_uart_receive_byte`函数的忙等待循环
- `uart_passthrough.c:381` - `uart_passthrough_software_rx_task`任务

## 修复方案

### 1. 优化忙等待循环
**修改前**：
```c
while (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
    if ((esp_timer_get_time() - start_time) > timeout_us) {
        return -1; // 超时
    }
    __asm__ __volatile__("nop");
}
```

**修改后**：
```c
while (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
    uint64_t current_time = esp_timer_get_time();
    
    // 检查超时
    if ((current_time - start_time) > timeout_us) {
        return -1; // 超时
    }
    
    // 定期让出CPU，避免看门狗超时
    if ((current_time - last_yield_time) > yield_interval_us) {
        ets_delay_us(10); // 10微秒的短暂延时
        last_yield_time = current_time;
    }
    
    __asm__ __volatile__("nop");
}
```

### 2. 改进接收任务策略
**修改前**：
```c
int rx_byte = soft_uart_receive_byte(100000); // 100ms超时
```

**修改后**：
```c
// 首先非阻塞检查是否有起始位
if (check_start_bit_non_blocking()) {
    int rx_byte = soft_uart_receive_byte(1000); // 1ms超时，快速处理
    // 处理接收到的数据
} else {
    // 根据空闲时间调整休眠策略
    if (consecutive_idle_count > max_idle_before_sleep) {
        vTaskDelay(pdMS_TO_TICKS(10)); // 10ms休眠
    } else {
        vTaskDelayUntil(&last_wake_time, check_period);
    }
}
```

### 3. 提高任务优先级
```c
// 修改前
#define UART_TASK_PRIORITY          1       // 最低优先级

// 修改后  
#define UART_TASK_PRIORITY          3       // 中等优先级
```

### 4. 添加看门狗管理
创建了 `watchdog_config.h` 提供：
- 安全的看门狗添加/移除函数
- 看门狗重置函数
- 看门狗友好的延时函数
- 看门狗健康检查函数

## 文件修改清单

### 修改的文件
1. `main/uart_passthrough.c` - 主要修复文件
   - 优化 `soft_uart_receive_byte` 函数
   - 改进 `uart_passthrough_software_rx_task` 任务
   - 添加非阻塞起始位检查函数

2. `main/system_config.h` - 配置修改
   - 提高UART任务优先级从1到3

### 新增的文件
1. `main/watchdog_config.h` - 看门狗管理工具
2. `main/watchdog_test.c` - 看门狗测试程序
3. `main/watchdog_test.h` - 测试程序头文件

## 测试验证

### 1. 编译测试
```bash
cd /path/to/project
idf.py build
```

### 2. 运行测试
```bash
idf.py -p /dev/ttyUSB0 flash monitor
```

### 3. 验证要点
- 系统启动后不再出现看门狗超时错误
- 软件UART功能正常工作
- CPU使用率合理，不会100%占用
- 其他任务能够正常运行

## 性能影响评估

### 优化效果
- **CPU占用率降低**：从接近100%降低到合理水平
- **响应性提升**：其他任务能够及时运行
- **稳定性改善**：消除看门狗超时错误

### 可能的副作用
- **接收延迟略微增加**：由于添加了CPU让出机制
- **功耗可能略微增加**：由于更频繁的任务切换

## 进一步优化建议

### 1. 硬件优化
- 考虑使用硬件UART替代软件UART
- 使用DMA减少CPU占用
- 使用中断驱动的接收方式

### 2. 软件优化
- 实现更智能的自适应超时机制
- 添加接收缓冲区管理
- 优化任务调度策略

### 3. 监控和调试
- 添加性能监控代码
- 实现运行时看门狗状态检查
- 添加详细的调试日志

## 注意事项

1. **测试充分性**：在实际应用环境中充分测试修复效果
2. **兼容性检查**：确保修改不影响其他功能模块
3. **性能监控**：持续监控系统性能和稳定性
4. **版本控制**：做好代码版本管理和回滚准备

## 联系信息

如有问题或需要进一步支持，请联系开发团队。
