#!/bin/bash

# 软件UART接收功能测试运行脚本

echo "=========================================="
echo "软件UART接收功能测试"
echo "=========================================="

# 检查ESP-IDF环境
if [ -z "$IDF_PATH" ]; then
    echo "错误: ESP-IDF环境未设置，请先运行 'source $HOME/esp/esp-idf/export.sh'"
    exit 1
fi

# 进入项目根目录
cd "$(dirname "$0")/.."

echo "正在构建测试..."
idf.py build

if [ $? -ne 0 ]; then
    echo "构建失败!"
    exit 1
fi

echo "构建成功!"
echo ""
echo "请连接ESP32S3开发板，然后按任意键继续..."
read -n 1 -s

echo "正在烧录并运行测试..."
idf.py flash monitor

echo "测试完成!"
