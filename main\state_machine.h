#ifndef STATE_MACHINE_H
#define STATE_MACHINE_H

#include "system_config.h"

/**
 * @brief 初始化状态机
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t state_machine_init(void);

/**
 * @brief 获取当前系统状态
 * @return 当前系统状态
 */
system_state_t state_machine_get_current_state(void);

/**
 * @brief 设置系统状态
 * @param new_state 新的系统状态
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t state_machine_set_state(system_state_t new_state);

/**
 * @brief 状态机主处理函数
 * 根据当前状态和接收到的事件进行状态转换
 * @param event 触发状态转换的事件
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t state_machine_process_event(at_command_type_t event);

/**
 * @brief 检查状态转换是否有效
 * @param current_state 当前状态
 * @param new_state 目标状态
 * @return true 转换有效，false 转换无效
 */
bool state_machine_is_transition_valid(system_state_t current_state, system_state_t new_state);

/**
 * @brief 获取状态名称字符串
 * @param state 状态枚举值
 * @return 状态名称字符串
 */
const char* state_machine_get_state_name(system_state_t state);

/**
 * @brief 状态机去初始化
 */
void state_machine_deinit(void);

#endif // STATE_MACHINE_H
