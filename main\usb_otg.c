#include "usb_otg.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "sys/stat.h"
#include "dirent.h"

static const char *TAG = "USB_OTG";

// USB挂载点
#define USB_MOUNT_POINT "/usb"

// USB设备状态
static bool usb_device_connected = false;
static bool usb_filesystem_mounted = false;

/**
 * @brief 初始化USB-OTG模块
 */
esp_err_t usb_otg_init(void)
{
    ESP_LOGI(TAG, "Initializing USB-OTG module...");
    
    // 创建USB互斥锁
    g_system_ctx.usb_mutex = xSemaphoreCreateMutex();
    if (g_system_ctx.usb_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create USB mutex");
        return ESP_FAIL;
    }
    
    // 初始化USB更新状态
    g_system_ctx.usb_update_status = USB_UPDATE_IDLE;
    
    ESP_LOGI(TAG, "USB-OTG module initialized");
    return ESP_OK;
}

/**
 * @brief 启动USB-OTG主机模式
 */
esp_err_t usb_otg_start_host_mode(void)
{
    ESP_LOGI(TAG, "Starting USB-OTG host mode...");
    
    // 注意：ESP32S3的USB-OTG主机模式需要特殊的配置
    // 这里提供一个简化的实现框架
    // 实际实现需要根据ESP-IDF的USB Host库进行配置
    
    // TODO: 实现USB Host初始化
    // 1. 配置USB Host库
    // 2. 注册设备连接/断开回调
    // 3. 启动USB Host任务
    
    ESP_LOGW(TAG, "USB-OTG host mode implementation is simplified");
    ESP_LOGI(TAG, "USB-OTG host mode started (simulated)");
    
    return ESP_OK;
}

/**
 * @brief 停止USB-OTG主机模式
 */
esp_err_t usb_otg_stop_host_mode(void)
{
    ESP_LOGI(TAG, "Stopping USB-OTG host mode...");
    
    // 卸载文件系统
    if (usb_filesystem_mounted) {
        usb_otg_unmount_fat_filesystem();
    }
    
    // TODO: 停止USB Host
    usb_device_connected = false;
    
    ESP_LOGI(TAG, "USB-OTG host mode stopped");
    return ESP_OK;
}

/**
 * @brief 检测U盘是否连接
 */
bool usb_otg_is_device_connected(void)
{
    // 简化实现：实际应该检查USB设备状态
    return usb_device_connected;
}

/**
 * @brief 挂载U盘FAT文件系统
 */
esp_err_t usb_otg_mount_fat_filesystem(void)
{
    if (usb_filesystem_mounted) {
        ESP_LOGW(TAG, "USB filesystem already mounted");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Mounting USB FAT filesystem...");

    // 简化实现：实际需要配置USB存储设备和FAT文件系统
    // 这里提供一个模拟实现，实际项目中需要根据ESP-IDF的USB Host和FAT文件系统API实现
    esp_err_t ret = ESP_OK; // 模拟成功
    
    if (ret == ESP_OK) {
        usb_filesystem_mounted = true;
        ESP_LOGI(TAG, "USB FAT filesystem mounted at %s", USB_MOUNT_POINT);
    } else {
        ESP_LOGE(TAG, "Failed to mount USB FAT filesystem: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * @brief 卸载U盘FAT文件系统
 */
esp_err_t usb_otg_unmount_fat_filesystem(void)
{
    if (!usb_filesystem_mounted) {
        ESP_LOGW(TAG, "USB filesystem not mounted");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Unmounting USB FAT filesystem...");
    
    // esp_err_t ret = esp_vfs_fat_usb_unmount(USB_MOUNT_POINT);
    esp_err_t ret = ESP_OK; // 模拟成功
    
    if (ret == ESP_OK) {
        usb_filesystem_mounted = false;
        ESP_LOGI(TAG, "USB FAT filesystem unmounted");
    } else {
        ESP_LOGE(TAG, "Failed to unmount USB FAT filesystem: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * @brief 检查目标文件是否存在
 */
bool usb_otg_check_file_exists(const char* filename)
{
    if (!usb_filesystem_mounted || filename == NULL) {
        return false;
    }
    
    char full_path[256];
    snprintf(full_path, sizeof(full_path), "%s/%s", USB_MOUNT_POINT, filename);
    
    struct stat file_stat;
    if (stat(full_path, &file_stat) == 0) {
        ESP_LOGI(TAG, "File %s exists (size: %ld bytes)", filename, file_stat.st_size);
        return true;
    } else {
        ESP_LOGW(TAG, "File %s not found", filename);
        return false;
    }
}

/**
 * @brief 检查所有目标文件是否存在
 */
bool usb_otg_check_all_target_files(void)
{
    bool file1_exists = usb_otg_check_file_exists(USB_TARGET_FILE1);
    bool file2_exists = usb_otg_check_file_exists(USB_TARGET_FILE2);
    
    bool all_exist = file1_exists && file2_exists;
    
    ESP_LOGI(TAG, "Target files check: %s=%s, %s=%s, all_exist=%s",
             USB_TARGET_FILE1, file1_exists ? "YES" : "NO",
             USB_TARGET_FILE2, file2_exists ? "YES" : "NO",
             all_exist ? "YES" : "NO");
    
    return all_exist;
}

/**
 * @brief 获取USB更新测试状态
 */
usb_update_status_t usb_otg_get_update_status(void)
{
    usb_update_status_t status;
    
    if (xSemaphoreTake(g_system_ctx.usb_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        status = g_system_ctx.usb_update_status;
        xSemaphoreGive(g_system_ctx.usb_mutex);
    } else {
        ESP_LOGW(TAG, "Failed to take USB mutex, returning cached status");
        status = g_system_ctx.usb_update_status;
    }
    
    return status;
}

/**
 * @brief 设置USB更新测试状态
 */
esp_err_t usb_otg_set_update_status(usb_update_status_t status)
{
    if (xSemaphoreTake(g_system_ctx.usb_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take USB mutex");
        return ESP_FAIL;
    }
    
    usb_update_status_t old_status = g_system_ctx.usb_update_status;
    g_system_ctx.usb_update_status = status;
    
    ESP_LOGI(TAG, "USB update status changed: %d -> %d", old_status, status);
    
    xSemaphoreGive(g_system_ctx.usb_mutex);
    return ESP_OK;
}

/**
 * @brief USB监控任务
 */
void usb_otg_monitor_task(void* pvParameters)
{
    ESP_LOGI(TAG, "USB monitor task started");

    TickType_t last_wake_time = xTaskGetTickCount();
    TickType_t monitor_period = pdMS_TO_TICKS(1000); // 1秒监控周期
    if (monitor_period == 0) {
        monitor_period = 1; // 至少1个tick
    }

    while (1) {
        // 检查USB设备连接状态
        // 简化实现：实际应该检查USB Host状态

        // 模拟USB设备连接检测
        static int connection_counter = 0;
        connection_counter++;

        // 模拟设备在10秒后连接
        if (connection_counter > 10 && !usb_device_connected) {
            usb_device_connected = true;
            ESP_LOGI(TAG, "USB device connected (simulated)");

            // 尝试挂载文件系统
            if (usb_otg_mount_fat_filesystem() == ESP_OK) {
                ESP_LOGI(TAG, "USB filesystem mounted successfully");
            }
        }

        // 如果设备已连接且文件系统已挂载，定期检查文件
        if (usb_device_connected && usb_filesystem_mounted) {
            // 检查目标文件（仅在USB更新测试期间）
            usb_update_status_t status = usb_otg_get_update_status();
            if (status == USB_UPDATE_IN_PROGRESS) {
                ESP_LOGI(TAG, "Checking target files during USB update test...");

                if (usb_otg_check_all_target_files()) {
                    usb_otg_set_update_status(USB_UPDATE_SUCCESS);
                    ESP_LOGI(TAG, "USB update test completed successfully");
                } else {
                    // 继续等待文件出现，或者在超时后设置为错误
                    static int check_counter = 0;
                    check_counter++;
                    if (check_counter > 30) { // 30秒超时
                        usb_otg_set_update_status(USB_UPDATE_ERROR);
                        ESP_LOGE(TAG, "USB update test failed - files not found");
                        check_counter = 0;
                    }
                }
            }
        }

        // 等待下一个监控周期
        vTaskDelayUntil(&last_wake_time, monitor_period);
    }
}

/**
 * @brief 启动USB监控任务
 */
esp_err_t usb_otg_start_monitor_task(void)
{
    if (g_system_ctx.usb_monitor_task_handle != NULL) {
        ESP_LOGW(TAG, "USB monitor task already running");
        return ESP_OK;
    }

    BaseType_t ret = xTaskCreate(
        usb_otg_monitor_task,
        "usb_monitor",
        USB_TASK_STACK_SIZE,
        NULL,
        USB_TASK_PRIORITY,
        &g_system_ctx.usb_monitor_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create USB monitor task");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "USB monitor task started");
    return ESP_OK;
}

/**
 * @brief 停止USB监控任务
 */
void usb_otg_stop_monitor_task(void)
{
    if (g_system_ctx.usb_monitor_task_handle != NULL) {
        vTaskDelete(g_system_ctx.usb_monitor_task_handle);
        g_system_ctx.usb_monitor_task_handle = NULL;
        ESP_LOGI(TAG, "USB monitor task stopped");
    }
}

/**
 * @brief 执行USB文件检测
 */
esp_err_t usb_otg_perform_file_detection(void)
{
    ESP_LOGI(TAG, "Performing USB file detection...");

    if (!usb_device_connected) {
        ESP_LOGW(TAG, "No USB device connected");
        return ESP_ERR_NOT_FOUND;
    }

    if (!usb_filesystem_mounted) {
        ESP_LOGW(TAG, "USB filesystem not mounted");
        return ESP_ERR_INVALID_STATE;
    }

    // 设置测试状态为进行中
    usb_otg_set_update_status(USB_UPDATE_IN_PROGRESS);

    // 检查目标文件
    if (usb_otg_check_all_target_files()) {
        usb_otg_set_update_status(USB_UPDATE_SUCCESS);
        ESP_LOGI(TAG, "USB file detection completed successfully");
        return ESP_OK;
    } else {
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        ESP_LOGE(TAG, "USB file detection failed");
        return ESP_ERR_NOT_FOUND;
    }
}

/**
 * @brief 获取USB设备信息
 */
esp_err_t usb_otg_get_device_info(char* device_info, int max_len)
{
    if (device_info == NULL || max_len <= 0) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!usb_device_connected) {
        snprintf(device_info, max_len, "No USB device connected");
        return ESP_ERR_NOT_FOUND;
    }

    // 简化实现：返回模拟的设备信息
    snprintf(device_info, max_len,
             "USB Device: Mass Storage\n"
             "Mounted: %s\n"
             "Mount Point: %s\n"
             "Status: %s",
             usb_filesystem_mounted ? "Yes" : "No",
             USB_MOUNT_POINT,
             usb_device_connected ? "Connected" : "Disconnected");

    return ESP_OK;
}

/**
 * @brief 去初始化USB-OTG模块
 */
void usb_otg_deinit(void)
{
    // 停止监控任务
    usb_otg_stop_monitor_task();

    // 停止USB主机模式
    usb_otg_stop_host_mode();

    // 删除互斥锁
    if (g_system_ctx.usb_mutex != NULL) {
        vSemaphoreDelete(g_system_ctx.usb_mutex);
        g_system_ctx.usb_mutex = NULL;
    }

    // 重置状态
    usb_device_connected = false;
    usb_filesystem_mounted = false;
    g_system_ctx.usb_update_status = USB_UPDATE_IDLE;

    ESP_LOGI(TAG, "USB-OTG module deinitialized");
}
