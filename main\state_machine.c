#include "state_machine.h"
#include "esp_log.h"

static const char *TAG = "STATE_MACHINE";

// 状态名称映射表
static const char* state_names[] = {
    "IDLE",
    "TESTING"
};

/**
 * @brief 初始化状态机
 */
esp_err_t state_machine_init(void)
{
    ESP_LOGI(TAG, "Initializing state machine...");
    
    // 创建状态互斥锁
    g_system_ctx.state_mutex = xSemaphoreCreateMutex();
    if (g_system_ctx.state_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create state mutex");
        return ESP_FAIL;
    }
    
    // 设置初始状态
    g_system_ctx.current_state = SYSTEM_STATE_IDLE;
    
    ESP_LOGI(TAG, "State machine initialized, initial state: %s", 
             state_machine_get_state_name(g_system_ctx.current_state));
    
    return ESP_OK;
}

/**
 * @brief 获取当前系统状态
 */
system_state_t state_machine_get_current_state(void)
{
    system_state_t state;
    
    if (xSemaphoreTake(g_system_ctx.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        state = g_system_ctx.current_state;
        xSemaphoreGive(g_system_ctx.state_mutex);
    } else {
        ESP_LOGW(TAG, "Failed to take state mutex, returning cached state");
        state = g_system_ctx.current_state;
    }
    
    return state;
}

/**
 * @brief 设置系统状态
 */
esp_err_t state_machine_set_state(system_state_t new_state)
{
    if (xSemaphoreTake(g_system_ctx.state_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take state mutex");
        return ESP_FAIL;
    }
    
    system_state_t old_state = g_system_ctx.current_state;
    
    // 检查状态转换是否有效
    if (!state_machine_is_transition_valid(old_state, new_state)) {
        ESP_LOGW(TAG, "Invalid state transition from %s to %s", 
                 state_machine_get_state_name(old_state),
                 state_machine_get_state_name(new_state));
        xSemaphoreGive(g_system_ctx.state_mutex);
        return ESP_ERR_INVALID_STATE;
    }
    
    g_system_ctx.current_state = new_state;
    
    ESP_LOGI(TAG, "State transition: %s -> %s", 
             state_machine_get_state_name(old_state),
             state_machine_get_state_name(new_state));
    
    xSemaphoreGive(g_system_ctx.state_mutex);
    
    return ESP_OK;
}

/**
 * @brief 状态机主处理函数
 */
esp_err_t state_machine_process_event(at_command_type_t event)
{
    system_state_t current_state = state_machine_get_current_state();
    esp_err_t ret = ESP_OK;
    
    ESP_LOGI(TAG, "Processing event %d in state %s", event, 
             state_machine_get_state_name(current_state));
    
    switch (current_state) {
        case SYSTEM_STATE_IDLE:
            if (event == AT_CMD_STAR) {
                // 收到AT_STAR指令，切换到测试状态
                ret = state_machine_set_state(SYSTEM_STATE_TESTING);
                if (ret == ESP_OK) {
                    ESP_LOGI(TAG, "System activated by AT_STAR command");
                }
            } else {
                ESP_LOGW(TAG, "Ignoring command %d in IDLE state", event);
            }
            break;
            
        case SYSTEM_STATE_TESTING:
            // 在测试状态下，所有AT指令都可以处理
            switch (event) {
                case AT_CMD_TEST_DEBUG:
                case AT_CMD_GET_GPIO_STA:
                case AT_CMD_TEST_USB_UPDATE:
                case AT_CMD_GET_TEST_USB_UPDATE_STA:
                    ESP_LOGI(TAG, "Processing command %d in TESTING state", event);
                    // 具体的命令处理由AT指令模块负责
                    break;
                    
                default:
                    ESP_LOGW(TAG, "Unknown command %d in TESTING state", event);
                    ret = ESP_ERR_NOT_SUPPORTED;
                    break;
            }
            break;
            
        default:
            ESP_LOGE(TAG, "Unknown system state: %d", current_state);
            ret = ESP_ERR_INVALID_STATE;
            break;
    }
    
    return ret;
}

/**
 * @brief 检查状态转换是否有效
 */
bool state_machine_is_transition_valid(system_state_t current_state, system_state_t new_state)
{
    // 定义有效的状态转换
    switch (current_state) {
        case SYSTEM_STATE_IDLE:
            // 从IDLE状态只能转换到TESTING状态
            return (new_state == SYSTEM_STATE_TESTING);
            
        case SYSTEM_STATE_TESTING:
            // 从TESTING状态可以保持在TESTING状态（用于状态刷新）
            // 或者回到IDLE状态（如果需要重置）
            return (new_state == SYSTEM_STATE_TESTING || new_state == SYSTEM_STATE_IDLE);
            
        default:
            return false;
    }
}

/**
 * @brief 获取状态名称字符串
 */
const char* state_machine_get_state_name(system_state_t state)
{
    if (state < sizeof(state_names) / sizeof(state_names[0])) {
        return state_names[state];
    }
    return "UNKNOWN";
}

/**
 * @brief 状态机去初始化
 */
void state_machine_deinit(void)
{
    if (g_system_ctx.state_mutex != NULL) {
        vSemaphoreDelete(g_system_ctx.state_mutex);
        g_system_ctx.state_mutex = NULL;
    }
    
    ESP_LOGI(TAG, "State machine deinitialized");
}
