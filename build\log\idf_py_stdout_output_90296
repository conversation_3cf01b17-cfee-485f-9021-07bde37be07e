-- Found Git: E:/ESP/IDF_TOOLS_PATH/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- ccache will be used for faster recompilation
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: E:/ESP/IDF_TOOLS_PATH/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: E:/ESP/IDF_TOOLS_PATH/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: E:/ESP/IDF_TOOLS_PATH/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file E:/ESP_project/GS068-01_test/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: e:/ESP/IDF_TOOLS_PATH/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "GS068-01_test" version: 1
-- Adding linker script E:/ESP_project/GS068-01_test/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script E:/ESP_project/GS068-01_test/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script E:/ESP/v5.3.3/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: E:/ESP/v5.3.3/esp-idf/components/app_trace E:/ESP/v5.3.3/esp-idf/components/app_update E:/ESP/v5.3.3/esp-idf/components/bootloader E:/ESP/v5.3.3/esp-idf/components/bootloader_support E:/ESP/v5.3.3/esp-idf/components/bt E:/ESP/v5.3.3/esp-idf/components/cmock E:/ESP/v5.3.3/esp-idf/components/console E:/ESP/v5.3.3/esp-idf/components/cxx E:/ESP/v5.3.3/esp-idf/components/driver E:/ESP/v5.3.3/esp-idf/components/efuse E:/ESP/v5.3.3/esp-idf/components/esp-tls E:/ESP/v5.3.3/esp-idf/components/esp_adc E:/ESP/v5.3.3/esp-idf/components/esp_app_format E:/ESP/v5.3.3/esp-idf/components/esp_bootloader_format E:/ESP/v5.3.3/esp-idf/components/esp_coex E:/ESP/v5.3.3/esp-idf/components/esp_common E:/ESP/v5.3.3/esp-idf/components/esp_driver_ana_cmpr E:/ESP/v5.3.3/esp-idf/components/esp_driver_cam E:/ESP/v5.3.3/esp-idf/components/esp_driver_dac E:/ESP/v5.3.3/esp-idf/components/esp_driver_gpio E:/ESP/v5.3.3/esp-idf/components/esp_driver_gptimer E:/ESP/v5.3.3/esp-idf/components/esp_driver_i2c E:/ESP/v5.3.3/esp-idf/components/esp_driver_i2s E:/ESP/v5.3.3/esp-idf/components/esp_driver_isp E:/ESP/v5.3.3/esp-idf/components/esp_driver_jpeg E:/ESP/v5.3.3/esp-idf/components/esp_driver_ledc E:/ESP/v5.3.3/esp-idf/components/esp_driver_mcpwm E:/ESP/v5.3.3/esp-idf/components/esp_driver_parlio E:/ESP/v5.3.3/esp-idf/components/esp_driver_pcnt E:/ESP/v5.3.3/esp-idf/components/esp_driver_ppa E:/ESP/v5.3.3/esp-idf/components/esp_driver_rmt E:/ESP/v5.3.3/esp-idf/components/esp_driver_sdio E:/ESP/v5.3.3/esp-idf/components/esp_driver_sdm E:/ESP/v5.3.3/esp-idf/components/esp_driver_sdmmc E:/ESP/v5.3.3/esp-idf/components/esp_driver_sdspi E:/ESP/v5.3.3/esp-idf/components/esp_driver_spi E:/ESP/v5.3.3/esp-idf/components/esp_driver_touch_sens E:/ESP/v5.3.3/esp-idf/components/esp_driver_tsens E:/ESP/v5.3.3/esp-idf/components/esp_driver_uart E:/ESP/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag E:/ESP/v5.3.3/esp-idf/components/esp_eth E:/ESP/v5.3.3/esp-idf/components/esp_event E:/ESP/v5.3.3/esp-idf/components/esp_gdbstub E:/ESP/v5.3.3/esp-idf/components/esp_hid E:/ESP/v5.3.3/esp-idf/components/esp_http_client E:/ESP/v5.3.3/esp-idf/components/esp_http_server E:/ESP/v5.3.3/esp-idf/components/esp_https_ota E:/ESP/v5.3.3/esp-idf/components/esp_https_server E:/ESP/v5.3.3/esp-idf/components/esp_hw_support E:/ESP/v5.3.3/esp-idf/components/esp_lcd E:/ESP/v5.3.3/esp-idf/components/esp_local_ctrl E:/ESP/v5.3.3/esp-idf/components/esp_mm E:/ESP/v5.3.3/esp-idf/components/esp_netif E:/ESP/v5.3.3/esp-idf/components/esp_netif_stack E:/ESP/v5.3.3/esp-idf/components/esp_partition E:/ESP/v5.3.3/esp-idf/components/esp_phy E:/ESP/v5.3.3/esp-idf/components/esp_pm E:/ESP/v5.3.3/esp-idf/components/esp_psram E:/ESP/v5.3.3/esp-idf/components/esp_ringbuf E:/ESP/v5.3.3/esp-idf/components/esp_rom E:/ESP/v5.3.3/esp-idf/components/esp_system E:/ESP/v5.3.3/esp-idf/components/esp_timer E:/ESP/v5.3.3/esp-idf/components/esp_vfs_console E:/ESP/v5.3.3/esp-idf/components/esp_wifi E:/ESP/v5.3.3/esp-idf/components/espcoredump E:/ESP/v5.3.3/esp-idf/components/esptool_py E:/ESP/v5.3.3/esp-idf/components/fatfs E:/ESP/v5.3.3/esp-idf/components/freertos E:/ESP/v5.3.3/esp-idf/components/hal E:/ESP/v5.3.3/esp-idf/components/heap E:/ESP/v5.3.3/esp-idf/components/http_parser E:/ESP/v5.3.3/esp-idf/components/idf_test E:/ESP/v5.3.3/esp-idf/components/ieee802154 E:/ESP/v5.3.3/esp-idf/components/json E:/ESP/v5.3.3/esp-idf/components/log E:/ESP/v5.3.3/esp-idf/components/lwip E:/ESP_project/GS068-01_test/main E:/ESP/v5.3.3/esp-idf/components/mbedtls E:/ESP/v5.3.3/esp-idf/components/mqtt E:/ESP/v5.3.3/esp-idf/components/newlib E:/ESP/v5.3.3/esp-idf/components/nvs_flash E:/ESP/v5.3.3/esp-idf/components/nvs_sec_provider E:/ESP/v5.3.3/esp-idf/components/openthread E:/ESP/v5.3.3/esp-idf/components/partition_table E:/ESP/v5.3.3/esp-idf/components/perfmon E:/ESP/v5.3.3/esp-idf/components/protobuf-c E:/ESP/v5.3.3/esp-idf/components/protocomm E:/ESP/v5.3.3/esp-idf/components/pthread E:/ESP/v5.3.3/esp-idf/components/sdmmc E:/ESP/v5.3.3/esp-idf/components/soc E:/ESP/v5.3.3/esp-idf/components/spi_flash E:/ESP/v5.3.3/esp-idf/components/spiffs E:/ESP/v5.3.3/esp-idf/components/tcp_transport E:/ESP/v5.3.3/esp-idf/components/touch_element E:/ESP/v5.3.3/esp-idf/components/ulp E:/ESP/v5.3.3/esp-idf/components/unity E:/ESP/v5.3.3/esp-idf/components/usb E:/ESP/v5.3.3/esp-idf/components/vfs E:/ESP/v5.3.3/esp-idf/components/wear_levelling E:/ESP/v5.3.3/esp-idf/components/wifi_provisioning E:/ESP/v5.3.3/esp-idf/components/wpa_supplicant E:/ESP/v5.3.3/esp-idf/components/xtensa
-- Configuring done (34.2s)
-- Generating done (0.7s)
-- Build files have been written to: E:/ESP_project/GS068-01_test/build
