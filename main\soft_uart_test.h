/**
 * @file soft_uart_test.h
 * @brief 基于Dedicated GPIO的软串口测试头文件
 */

#ifndef SOFT_UART_TEST_H
#define SOFT_UART_TEST_H

#include <stdint.h>
#include <string.h>
#include "esp_timer.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 软串口性能测试
 * 测试基本的发送和接收功能
 */
void soft_uart_performance_test(void);

/**
 * @brief 软串口连续发送测试
 * @param duration_seconds 测试持续时间（秒）
 */
void soft_uart_continuous_test(int duration_seconds);

/**
 * @brief 软串口回环测试
 * 需要将TX和RX引脚短接进行测试
 */
void soft_uart_loopback_test(void);

/**
 * @brief 运行所有软串口测试
 * 包括性能测试、回环测试和连续测试
 */
void run_all_soft_uart_tests(void);

#ifdef __cplusplus
}
#endif

#endif // SOFT_UART_TEST_H
