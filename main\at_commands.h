#ifndef AT_COMMANDS_H
#define AT_COMMANDS_H

#include "system_config.h"

/**
 * @brief 初始化AT指令处理模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_commands_init(void);

/**
 * @brief 解析AT指令字符串
 * @param cmd_str 输入的AT指令字符串
 * @param cmd 输出的AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_commands_parse(const char* cmd_str, at_command_t* cmd);

/**
 * @brief 处理AT指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_commands_process(at_command_t* cmd);

/**
 * @brief 发送AT指令响应
 * @param response 响应字符串
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_commands_send_response(const char* response);

/**
 * @brief 处理AT_STAR指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_cmd_handle_star(at_command_t* cmd);

/**
 * @brief 处理AT_TEST_DEBUG指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_cmd_handle_test_debug(at_command_t* cmd);

/**
 * @brief 处理AT_GET_GPIO_STA指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_cmd_handle_get_gpio_sta(at_command_t* cmd);

/**
 * @brief 处理AT_TEST_USB_UPDATE指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_cmd_handle_test_usb_update(at_command_t* cmd);

/**
 * @brief 处理AT_GET_TEST_USB_UPDATE_STA指令
 * @param cmd AT指令结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t at_cmd_handle_get_usb_update_sta(at_command_t* cmd);

/**
 * @brief AT指令处理任务
 * @param pvParameters 任务参数
 */
void at_commands_task(void* pvParameters);

/**
 * @brief 去初始化AT指令处理模块
 */
void at_commands_deinit(void);

#endif // AT_COMMANDS_H
