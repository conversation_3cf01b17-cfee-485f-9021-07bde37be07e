/**
 * @file soft_uart_integration_example.c
 * @brief 展示如何在main.c中集成新的Dedicated GPIO软串口
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "uart_passthrough.h"
#include "soft_uart_test.h"
#include "system_config.h"
#include <inttypes.h>

static const char *TAG = "SOFT_UART_EXAMPLE";

/**
 * @brief 软串口初始化和测试任务
 */
void soft_uart_demo_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Starting Dedicated GPIO Software UART demo...");
    
    // 等待系统稳定
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 初始化软串口模块
    ESP_LOGI(TAG, "Initializing UART passthrough module...");
    esp_err_t ret = uart_passthrough_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize UART passthrough: %s", esp_err_to_name(ret));
        vTaskDelete(NULL);
        return;
    }
    
    // 启动透传任务
    ESP_LOGI(TAG, "Starting UART passthrough tasks...");
    ret = uart_passthrough_start_tasks();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start UART passthrough tasks: %s", esp_err_to_name(ret));
        vTaskDelete(NULL);
        return;
    }
    
    // 等待任务启动
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    ESP_LOGI(TAG, "Software UART is now running!");
    ESP_LOGI(TAG, "TX Pin: %d, RX Pin: %d", PASSTHROUGH2_TXD, PASSTHROUGH2_RXD);
    ESP_LOGI(TAG, "Baud Rate: 115200");
    
    // 可选：运行测试套件
    #ifdef CONFIG_SOFT_UART_RUN_TESTS
    ESP_LOGI(TAG, "Running software UART tests...");
    run_all_soft_uart_tests();
    #endif
    
    // 主循环：定期显示统计信息
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(10000)); // 每10秒显示一次统计
        
        uint32_t tx_bytes, rx_bytes;
        if (uart_passthrough_get_stats(UART_NUM_MAX, &tx_bytes, &rx_bytes) == ESP_OK) {
            uint32_t errors = uart_passthrough_get_soft_uart_errors();
            ESP_LOGI(TAG, "Software UART Stats - TX: %" PRIu32 " bytes, RX: %" PRIu32 " bytes, Errors: %" PRIu32,
                     tx_bytes, rx_bytes, errors);
            
            // 计算错误率
            if (rx_bytes > 0) {
                float error_rate = (float)errors / (rx_bytes + errors) * 100.0f;
                ESP_LOGI(TAG, "Error rate: %.2f%%", error_rate);
            }
        }
    }
}

/**
 * @brief 在main.c中调用此函数来启动软串口演示
 */
void start_soft_uart_demo(void)
{
    // 创建软串口演示任务
    BaseType_t ret = xTaskCreate(
        soft_uart_demo_task,
        "soft_uart_demo",
        4096,  // 栈大小
        NULL,
        5,     // 优先级
        NULL
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create soft UART demo task");
    } else {
        ESP_LOGI(TAG, "Soft UART demo task created successfully");
    }
}

/**
 * @brief 软串口数据发送示例
 */
void soft_uart_send_example(void)
{
    const char *messages[] = {
        "Hello from Dedicated GPIO Software UART!",
        "Testing high-performance UART implementation",
        "ESP32-S3 Dedicated GPIO rocks!",
        "115200 baud rate achieved",
        "End of test messages"
    };
    
    int msg_count = sizeof(messages) / sizeof(messages[0]);
    
    ESP_LOGI(TAG, "Sending %d test messages...", msg_count);
    
    for (int i = 0; i < msg_count; i++) {
        ESP_LOGI(TAG, "Sending message %d: %s", i + 1, messages[i]);
        
        int sent = uart_passthrough_software_send(
            (const uint8_t*)messages[i], 
            strlen(messages[i])
        );
        
        if (sent == strlen(messages[i])) {
            ESP_LOGI(TAG, "Message %d sent successfully (%d bytes)", i + 1, sent);
        } else {
            ESP_LOGW(TAG, "Message %d partially sent (%d/%d bytes)", 
                     i + 1, sent, strlen(messages[i]));
        }
        
        // 发送换行符
        uart_passthrough_software_send((const uint8_t*)"\r\n", 2);
        
        // 消息间隔
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    ESP_LOGI(TAG, "All test messages sent");
}

/**
 * @brief 软串口数据接收示例
 */
void soft_uart_receive_example(int timeout_seconds)
{
    ESP_LOGI(TAG, "Listening for incoming data for %d seconds...", timeout_seconds);
    
    uint8_t buffer[256];
    uint64_t start_time = esp_timer_get_time();
    uint64_t end_time = start_time + (timeout_seconds * 1000000ULL);
    
    int total_received = 0;
    
    while (esp_timer_get_time() < end_time) {
        int received = uart_passthrough_software_receive(
            buffer, 
            sizeof(buffer) - 1, 
            100  // 100ms timeout per receive
        );
        
        if (received > 0) {
            buffer[received] = '\0';
            ESP_LOGI(TAG, "Received %d bytes: %s", received, (char*)buffer);
            total_received += received;
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    ESP_LOGI(TAG, "Receive test completed. Total received: %d bytes", total_received);
}

/**
 * @brief 软串口交互式测试
 * 发送数据并等待回应
 */
void soft_uart_interactive_test(void)
{
    ESP_LOGI(TAG, "Starting interactive test...");
    ESP_LOGI(TAG, "This test sends data and waits for echo response");
    
    const char test_data[] = "PING";
    uint8_t response[64];
    
    for (int i = 0; i < 5; i++) {
        ESP_LOGI(TAG, "Test %d: Sending '%s'", i + 1, test_data);
        
        // 发送测试数据
        int sent = uart_passthrough_software_send(
            (const uint8_t*)test_data, 
            strlen(test_data)
        );
        
        if (sent != strlen(test_data)) {
            ESP_LOGW(TAG, "Test %d: Failed to send complete data", i + 1);
            continue;
        }
        
        // 等待响应
        vTaskDelay(pdMS_TO_TICKS(100));
        
        int received = uart_passthrough_software_receive(
            response, 
            sizeof(response) - 1, 
            1000
        );
        
        if (received > 0) {
            response[received] = '\0';
            ESP_LOGI(TAG, "Test %d: Received response '%s'", i + 1, (char*)response);
        } else {
            ESP_LOGW(TAG, "Test %d: No response received", i + 1);
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    ESP_LOGI(TAG, "Interactive test completed");
}

/* 
 * 在main.c中的使用示例：
 * 
 * void app_main(void)
 * {
 *     // 其他初始化代码...
 *     
 *     // 启动软串口演示
 *     start_soft_uart_demo();
 *     
 *     // 或者手动测试特定功能
 *     // soft_uart_send_example();
 *     // soft_uart_receive_example(30);
 *     // soft_uart_interactive_test();
 * }
 */
