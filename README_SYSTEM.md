# ESP32S3 多串口GPIO测试系统

## 系统概述

这是一个基于ESP32S3的多串口和GPIO测试系统，主要用于与Linux板子进行通信测试和GPIO状态监控。

## 功能特性

### 1. 多串口支持
- **UART1 (Linux DEBUG)**: GPIO4(TX), GPIO5(RX) - 与Linux板子DEBUG口通信
- **UART2 (回传串口1)**: GPIO6(TX), GPIO7(RX) - 回传接收到的数据
- **UART0 (回传串口2)**: GPIO1(TX), GPIO3(RX) - 回传接收到的数据
- **软串口 (GPIO状态)**: GPIO17(TX), GPIO18(RX) - GPIO状态查询

### 2. GPIO输入监控
- 12个GPIO输入引脚: GPIO8, 9, 10, 11, 12, 13, 14, 21, 47, 48, 45, 46
- 支持AT指令查询GPIO状态
- 定期状态监控和日志输出

### 3. Linux板子自动化测试
- 自动检测登录提示并发送root凭据
- 执行预定义的测试命令列表
- 记录测试结果并保存到Linux板子

## 工作流程

1. **系统初始化**
   - 初始化4个串口（3个硬件串口 + 1个软串口）
   - 配置12个GPIO输入引脚
   - 创建必要的任务和队列

2. **等待Linux连接**
   - 监听Linux DEBUG串口数据
   - 检测登录提示

3. **自动登录**
   - 发送"root"用户名
   - 等待命令行提示符

4. **执行测试命令**
   - 逐个执行预定义的Linux测试命令
   - 记录每个命令的执行结果

5. **保存测试结果**
   - 使用cat命令将结果写入Linux板子的/tmp/test_results.txt

## 测试命令列表

系统会自动执行以下Linux命令：
- `uname -a` - 系统信息
- `free -m` - 内存使用情况
- `df -h` - 磁盘使用情况
- `ps aux | head -10` - 进程列表
- `ifconfig` - 网络配置
- `dmesg | tail -5` - 系统消息
- `cat /proc/cpuinfo | head -10` - CPU信息
- `uptime` - 系统运行时间
- `whoami` - 当前用户
- `pwd` - 当前目录

## GPIO状态查询

通过软串口发送以下AT指令可查询GPIO状态：
- `AT+GPIO?` - 查询所有GPIO输入状态
- `GPIO_STATUS` - 查询所有GPIO输入状态

返回格式：`GPIO_STATUS:G8:1,G9:0,G10:1,...`

## 编译和烧录

1. 确保ESP-IDF环境已正确配置
2. 在项目根目录执行：
   ```bash
   idf.py build
   idf.py flash monitor
   ```

## 注意事项

1. ESP32S3的UART0默认用于日志输出（GPIO43/44），本系统重新配置了UART0的引脚
2. 软串口实现较为简化，实际应用中可能需要更精确的位时序控制
3. 系统会保留ESP32S3的默认日志串口功能
4. 所有串口波特率均设置为115200

## 系统状态

系统运行时会显示以下状态：
- `INIT` - 初始化中
- `WAITING_LOGIN` - 等待Linux登录提示
- `LOGGED_IN` - 已登录，等待命令行
- `TESTING` - 正在执行测试命令
- `COMPLETE` - 测试完成

## 故障排除

1. 如果无法检测到登录提示，检查Linux DEBUG串口连接
2. 如果测试命令执行失败，检查Linux板子是否正常启动
3. 如果GPIO状态查询无响应，检查软串口连接和AT指令格式
