@echo off
echo ========================================
echo ESP32S3 编译测试脚本
echo ========================================

REM 设置ESP-IDF环境
set IDF_PATH=E:\ESP\v5.3.3\esp-idf
set IDF_TOOLS_PATH=E:\ESP\IDF_TOOLS_PATH

echo 设置ESP-IDF环境...
call "%IDF_PATH%\export.bat"

if %ERRORLEVEL% NEQ 0 (
    echo 错误: ESP-IDF环境设置失败
    pause
    exit /b 1
)

echo.
echo 当前目录: %CD%
echo IDF_PATH: %IDF_PATH%

echo.
echo 清理构建目录...
if exist build (
    rmdir /s /q build
    echo 构建目录已清理
)

echo.
echo 设置目标芯片为ESP32S3...
idf.py set-target esp32s3

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 设置目标芯片失败
    pause
    exit /b 1
)

echo.
echo 开始编译项目...
idf.py build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo.
    echo 生成的文件:
    if exist build\GS068-01_test.bin (
        echo   - build\GS068-01_test.bin
        for %%I in (build\GS068-01_test.bin) do echo     大小: %%~zI bytes
    )
    if exist build\GS068-01_test.elf (
        echo   - build\GS068-01_test.elf
        for %%I in (build\GS068-01_test.elf) do echo     大小: %%~zI bytes
    )
    if exist build\bootloader\bootloader.bin (
        echo   - build\bootloader\bootloader.bin
        for %%I in (build\bootloader\bootloader.bin) do echo     大小: %%~zI bytes
    )
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo.
    echo 请检查上述错误信息。
    echo.
    echo 常见问题解决方案:
    echo 1. 确保ESP-IDF环境正确安装
    echo 2. 检查代码中是否有语法错误
    echo 3. 确保所有依赖的头文件都存在
    echo 4. 如果是ets_delay_us错误，请检查是否使用了esp_compat_delay_us
)

echo.
pause
