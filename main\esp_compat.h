#ifndef ESP_COMPAT_H
#define ESP_COMPAT_H

/**
 * @brief ESP-IDF版本兼容性头文件
 * 
 * 这个文件提供了不同ESP-IDF版本之间的兼容性支持，
 * 主要处理API变更和函数重命名问题。
 */

#include "esp_idf_version.h"
#include "esp_log.h"

// ESP-IDF版本检查
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF v5.0及以上版本
    #include "esp_rom_sys.h"
    #define COMPAT_DELAY_US(us) esp_rom_delay_us(us)
    #define COMPAT_HAS_ROM_DELAY 1
#else
    // ESP-IDF v4.x版本
    #include "rom/ets_sys.h"
    #define COMPAT_DELAY_US(us) ets_delay_us(us)
    #define COMPAT_HAS_ROM_DELAY 1
#endif

// 其他可能的兼容性定义
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 1, 0)
    #define COMPAT_HAS_DEDICATED_GPIO 1
#else
    #define COMPAT_HAS_DEDICATED_GPIO 0
#endif

// 日志兼容性
#ifndef ESP_LOGD_EARLY
    #define ESP_LOGD_EARLY(tag, format, ...) ESP_LOGD(tag, format, ##__VA_ARGS__)
#endif

#ifndef ESP_LOGI_EARLY
    #define ESP_LOGI_EARLY(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#endif

// 任务看门狗兼容性
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    #include "esp_task_wdt.h"
    #define COMPAT_TASK_WDT_ADD(handle) esp_task_wdt_add(handle)
    #define COMPAT_TASK_WDT_DELETE(handle) esp_task_wdt_delete(handle)
    #define COMPAT_TASK_WDT_RESET() esp_task_wdt_reset()
#else
    #include "esp_task_wdt.h"
    #define COMPAT_TASK_WDT_ADD(handle) esp_task_wdt_add(handle)
    #define COMPAT_TASK_WDT_DELETE(handle) esp_task_wdt_delete(handle)
    #define COMPAT_TASK_WDT_RESET() esp_task_wdt_reset()
#endif

// GPIO兼容性
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    #include "driver/gpio.h"
    #define COMPAT_GPIO_SET_LEVEL(gpio, level) gpio_set_level(gpio, level)
    #define COMPAT_GPIO_GET_LEVEL(gpio) gpio_get_level(gpio)
#else
    #include "driver/gpio.h"
    #define COMPAT_GPIO_SET_LEVEL(gpio, level) gpio_set_level(gpio, level)
    #define COMPAT_GPIO_GET_LEVEL(gpio) gpio_get_level(gpio)
#endif

// 定时器兼容性
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    #include "esp_timer.h"
    #define COMPAT_TIMER_GET_TIME() esp_timer_get_time()
#else
    #include "esp_timer.h"
    #define COMPAT_TIMER_GET_TIME() esp_timer_get_time()
#endif

// 内存管理兼容性
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    #include "esp_heap_caps.h"
    #define COMPAT_MALLOC(size) heap_caps_malloc(size, MALLOC_CAP_DEFAULT)
    #define COMPAT_FREE(ptr) heap_caps_free(ptr)
#else
    #include "esp_heap_caps.h"
    #define COMPAT_MALLOC(size) heap_caps_malloc(size, MALLOC_CAP_DEFAULT)
    #define COMPAT_FREE(ptr) heap_caps_free(ptr)
#endif

// 版本信息宏
#define ESP_COMPAT_VERSION_MAJOR ESP_IDF_VERSION_MAJOR
#define ESP_COMPAT_VERSION_MINOR ESP_IDF_VERSION_MINOR
#define ESP_COMPAT_VERSION_PATCH ESP_IDF_VERSION_PATCH

/**
 * @brief 获取ESP-IDF版本字符串
 * @return 版本字符串
 */
static inline const char* esp_compat_get_version_string(void)
{
    return IDF_VER;
}

/**
 * @brief 检查ESP-IDF版本是否满足最低要求
 * @param major 主版本号
 * @param minor 次版本号
 * @param patch 补丁版本号
 * @return true 满足要求，false 不满足
 */
static inline bool esp_compat_version_check(int major, int minor, int patch)
{
    return ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(major, minor, patch);
}

/**
 * @brief 安全的微秒延时函数
 * @param us 延时时间（微秒）
 */
static inline void esp_compat_delay_us(uint32_t us)
{
    if (us == 0) return;
    
#if COMPAT_HAS_ROM_DELAY
    if (us <= 16383) { // ROM函数的最大延时限制
        COMPAT_DELAY_US(us);
    } else {
        // 长延时分段处理
        while (us > 16383) {
            COMPAT_DELAY_US(16383);
            us -= 16383;
        }
        if (us > 0) {
            COMPAT_DELAY_US(us);
        }
    }
#else
    // 如果没有ROM延时函数，使用任务延时
    TickType_t ticks = pdMS_TO_TICKS((us + 999) / 1000);
    if (ticks == 0) ticks = 1;
    vTaskDelay(ticks);
#endif
}

// 调试信息
#ifdef CONFIG_ESP_COMPAT_DEBUG
    #define ESP_COMPAT_LOGD(tag, format, ...) ESP_LOGD(tag, "[COMPAT] " format, ##__VA_ARGS__)
    #define ESP_COMPAT_LOGI(tag, format, ...) ESP_LOGI(tag, "[COMPAT] " format, ##__VA_ARGS__)
    #define ESP_COMPAT_LOGW(tag, format, ...) ESP_LOGW(tag, "[COMPAT] " format, ##__VA_ARGS__)
    #define ESP_COMPAT_LOGE(tag, format, ...) ESP_LOGE(tag, "[COMPAT] " format, ##__VA_ARGS__)
#else
    #define ESP_COMPAT_LOGD(tag, format, ...)
    #define ESP_COMPAT_LOGI(tag, format, ...)
    #define ESP_COMPAT_LOGW(tag, format, ...)
    #define ESP_COMPAT_LOGE(tag, format, ...)
#endif

#endif // ESP_COMPAT_H
