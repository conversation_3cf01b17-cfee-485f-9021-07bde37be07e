{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "e:\\ESP\\v5.3.3\\esp-idf", "idf.pythonInstallPath": "e:\\ESP\\IDF_TOOLS_PATH\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.portWin": "COM15", "idf.toolsPathWin": "e:\\ESP\\IDF_TOOLS_PATH", "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "e:\\ESP\\IDF_TOOLS_PATH\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=e:\\ESP\\IDF_TOOLS_PATH\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe", "--compile-commands-dir=${workspaceFolder}\\build"], "idf.flashType": "UART"}