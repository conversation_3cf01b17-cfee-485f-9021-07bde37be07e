#include "watchdog_test.h"
#include "watchdog_config.h"
#include "system_config.h"
#include "esp_compat.h"    // ESP-IDF版本兼容性支持
#include "esp_log.h"
#include "esp_timer.h"

static const char *TAG = "WDT_TEST";

/**
 * @brief 测试看门狗功能
 */
void watchdog_test_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Watchdog test task started");
    
    // 添加当前任务到看门狗
    watchdog_add_current_task("watchdog_test");
    
    uint32_t iteration = 0;
    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (1) {
        iteration++;
        
        // 模拟一些工作
        ESP_LOGI(TAG, "Watchdog test iteration: %lu", iteration);
        
        // 测试不同的延时策略
        if (iteration % 10 == 0) {
            ESP_LOGI(TAG, "Testing watchdog-friendly long delay...");
            watchdog_friendly_delay(2000, true); // 2秒延时，重置看门狗
        } else {
            // 正常的短延时
            vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(500));
            watchdog_reset();
        }
        
        // 检查看门狗健康状态
        if (!watchdog_is_healthy()) {
            ESP_LOGW(TAG, "Watchdog health check failed!");
        }
        
        // 每50次迭代后停止测试
        if (iteration >= 50) {
            ESP_LOGI(TAG, "Watchdog test completed successfully");
            break;
        }
    }
    
    // 从看门狗移除任务
    watchdog_remove_current_task("watchdog_test");
    
    ESP_LOGI(TAG, "Watchdog test task finished");
    vTaskDelete(NULL);
}

/**
 * @brief 启动看门狗测试任务
 */
esp_err_t watchdog_test_start(void)
{
    BaseType_t ret = xTaskCreate(
        watchdog_test_task,
        "watchdog_test",
        SYSTEM_TASK_STACK_SIZE,
        NULL,
        2, // 中等优先级
        NULL
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create watchdog test task");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Watchdog test task created");
    return ESP_OK;
}

/**
 * @brief 测试软件UART接收函数的看门狗兼容性
 */
void test_soft_uart_watchdog_compatibility(void)
{
    ESP_LOGI(TAG, "Testing software UART watchdog compatibility...");
    
    // 添加当前任务到看门狗
    watchdog_add_current_task("uart_test");
    
    uint64_t start_time = esp_timer_get_time();
    const uint64_t test_duration_us = 3000000; // 3秒测试
    uint32_t test_iterations = 0;
    
    while ((esp_timer_get_time() - start_time) < test_duration_us) {
        test_iterations++;
        
        // 模拟软件UART接收循环
        uint64_t loop_start = esp_timer_get_time();
        uint64_t last_yield = loop_start;
        const uint64_t yield_interval = 1000; // 1ms
        const uint64_t loop_timeout = 10000; // 10ms
        
        // 模拟等待起始位的循环
        while ((esp_timer_get_time() - loop_start) < loop_timeout) {
            uint64_t current_time = esp_timer_get_time();
            
            // 定期让出CPU
            if ((current_time - last_yield) > yield_interval) {
                esp_compat_delay_us(10); // 使用兼容性延时函数
                last_yield = current_time;
            }
            
            __asm__ __volatile__("nop");
        }
        
        // 每100次迭代重置看门狗
        if (test_iterations % 100 == 0) {
            watchdog_reset();
            ESP_LOGD(TAG, "UART test iteration: %lu", test_iterations);
        }
        
        // 短暂延时
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    ESP_LOGI(TAG, "Software UART watchdog test completed. Iterations: %lu", test_iterations);
    
    // 从看门狗移除任务
    watchdog_remove_current_task("uart_test");
}
