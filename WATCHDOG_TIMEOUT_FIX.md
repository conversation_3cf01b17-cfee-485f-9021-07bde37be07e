# ESP32S3 任务看门狗超时修复方案

## 🔍 问题描述

系统出现任务看门狗超时错误，具体表现为：

```
E (12275293) task_wdt: Task watchdog got triggered. The following tasks/users did not reset the watchdog in time:
E (12275293) task_wdt:  - IDLE1 (CPU 1)
E (12275293) task_wdt: Tasks currently running:
E (12275293) task_wdt: CPU 0: IDLE0
E (12275293) task_wdt: CPU 1: soft_uart_rx
```

**堆栈回溯显示问题发生在：**
- `soft_uart_receive_byte` 函数 (uart_passthrough.c:151行)
- `uart_passthrough_software_rx_task` 任务 (uart_passthrough.c:417行)

## 📋 根本原因分析

### 1. 核心问题
- **长时间临界区**: `soft_uart_receive_byte`函数中的临界区持续时间过长
- **CPU占用过高**: 软件UART接收任务长时间占用CPU 1，阻止IDLE1任务运行
- **看门狗无法重置**: IDLE1任务无法获得CPU时间来重置看门狗

### 2. 具体问题点
1. **临界区设计不当**: 整个字节接收过程都在临界区内
2. **忙等待循环**: 等待起始位的while循环虽有让出机制，但仍可能长时间占用CPU
3. **缺少看门狗管理**: 软件UART任务没有主动重置看门狗

### 3. 看门狗配置
- **任务看门狗超时**: 5秒 (`CONFIG_ESP_TASK_WDT_TIMEOUT_S=5`)
- **双核监控**: 启用CPU0和CPU1的IDLE任务监控
- **看门狗重置间隔**: 建议1秒内至少重置一次

## 🛠️ 修复方案

### 1. 优化软件UART接收函数

#### 改进临界区管理
```c
// 修改前：整个接收过程在临界区内
taskENTER_CRITICAL(&mux);
// ... 长时间的位接收过程
taskEXIT_CRITICAL(&mux);

// 修改后：分段临界区，减少阻塞时间
for (int i = 0; i < 8; i++) {
    taskEXIT_CRITICAL(&mux);        // 退出临界区进行延时
    delay_us_precise(g_soft_uart.bit_time_us);
    taskENTER_CRITICAL(&mux);       // 进入临界区进行采样
    
    if (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
        rx_byte |= (1 << i);
    }
}
```

#### 增强CPU让出机制
```c
// 修改前：每1ms让出一次CPU
const uint64_t yield_interval_us = 1000;
esp_compat_delay_us(10);

// 修改后：更频繁的CPU让出和看门狗重置
const uint64_t yield_interval_us = 500;        // 每500us让出一次
const uint64_t wdt_reset_interval_us = 1000000; // 每1秒重置看门狗

if ((current_time - last_yield_time) > yield_interval_us) {
    taskYIELD();  // 使用taskYIELD()更高效
    last_yield_time = current_time;
}

if ((current_time - last_wdt_reset_time) > wdt_reset_interval_us) {
    watchdog_reset();
    last_wdt_reset_time = current_time;
}
```

### 2. 优化软件UART接收任务

#### 添加看门狗管理
```c
void uart_passthrough_software_rx_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Dedicated GPIO Software UART RX task started");

    // 将任务添加到看门狗监控
    esp_err_t wdt_ret = watchdog_add_current_task("soft_uart_rx");
    if (wdt_ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to add task to watchdog, continuing anyway");
    }

    uint32_t wdt_reset_counter = 0;
    const uint32_t wdt_reset_interval = 100; // 每100次循环重置一次看门狗

    while (1) {
        // 定期重置看门狗
        wdt_reset_counter++;
        if (wdt_reset_counter >= wdt_reset_interval) {
            watchdog_reset();
            wdt_reset_counter = 0;
        }
        
        // ... 任务逻辑
    }
}
```

#### 改进休眠策略
```c
// 修改前：简单的vTaskDelay
vTaskDelay(pdMS_TO_TICKS(50));

// 修改后：看门狗友好的延时
watchdog_friendly_delay(30, true); // 30ms看门狗友好休眠
```

### 3. 优化UART阻塞操作

#### uboot_operations.c中的改进
```c
// 修改前：长时间阻塞的UART读取
int rx_len = uart_read_bytes(UART_DEBUG, rx_buffer, RX_BUF_SIZE - 1, pdMS_TO_TICKS(100));

// 修改后：添加看门狗重置和较短超时
uint32_t wdt_reset_counter = 0;
const uint32_t wdt_reset_interval = 10;

while (1) {
    // 定期重置看门狗
    wdt_reset_counter++;
    if (wdt_reset_counter >= wdt_reset_interval) {
        watchdog_reset();
        wdt_reset_counter = 0;
    }
    
    // 使用较短的超时时间
    int rx_len = uart_read_bytes(UART_DEBUG, rx_buffer, RX_BUF_SIZE - 1, pdMS_TO_TICKS(50));
    // ... 处理逻辑
}
```

## 📁 修改的文件

### 1. main/uart_passthrough.c
- **优化**: `soft_uart_receive_byte`函数的临界区管理
- **增强**: CPU让出机制和看门狗重置
- **改进**: `uart_passthrough_software_rx_task`任务的看门狗管理

### 2. main/uboot_operations.c
- **添加**: 看门狗头文件包含
- **优化**: UART读取操作的看门狗重置
- **改进**: 阻塞超时时间设置

### 3. 使用的工具函数
- **watchdog_config.h**: 看门狗管理工具函数
- **watchdog_friendly_delay()**: 看门狗友好的延时函数
- **watchdog_reset()**: 安全的看门狗重置函数

## 🧪 验证步骤

### 1. 编译验证
```bash
# 清理构建
idf.py fullclean

# 构建项目
idf.py build
```

### 2. 运行测试
```bash
# 烧录并监控
idf.py -p COM15 flash monitor
```

### 3. 预期结果
- ✅ 无任务看门狗超时错误
- ✅ 软件UART功能正常
- ✅ 系统稳定运行
- ✅ CPU负载均衡

## 📊 性能影响分析

### 1. 正面影响
- **稳定性提升**: 消除看门狗超时导致的系统重启
- **响应性改善**: 更频繁的CPU让出提高系统响应性
- **可靠性增强**: 主动的看门狗管理提高系统可靠性

### 2. 性能开销
- **CPU开销**: 增加少量看门狗重置和任务切换开销
- **内存开销**: 增加少量变量存储（可忽略）
- **时序影响**: 分段临界区可能轻微影响UART时序精度

### 3. 优化平衡
- **时序 vs 稳定性**: 在保证UART功能的前提下优先保证系统稳定性
- **性能 vs 可靠性**: 少量性能开销换取显著的可靠性提升

## 🚀 最佳实践建议

### 1. 看门狗管理原则
- 所有长时间运行的任务都应主动管理看门狗
- 在可能长时间阻塞的操作前后重置看门狗
- 使用看门狗友好的延时函数

### 2. 临界区设计原则
- 尽量缩短临界区持续时间
- 避免在临界区内进行长时间操作
- 必要时使用分段临界区

### 3. 任务调度优化
- 合理设置任务优先级
- 在忙等待循环中主动让出CPU
- 使用适当的延时策略

## 📈 修复效果

### 1. 问题解决
- ✅ **消除看门狗超时**: 软件UART任务不再长时间占用CPU
- ✅ **系统稳定性**: 避免因看门狗超时导致的系统重启
- ✅ **任务调度**: IDLE任务能够正常运行和重置看门狗

### 2. 系统改善
- ✅ **响应性提升**: 更好的任务调度和CPU利用率
- ✅ **可维护性**: 统一的看门狗管理模式
- ✅ **可扩展性**: 为其他类似问题提供解决模板
