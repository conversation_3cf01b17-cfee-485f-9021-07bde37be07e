#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include "system_config.h"

// 错误代码定义
typedef enum {
    ERROR_NONE = 0,
    ERROR_UART_INIT_FAILED,
    ERROR_GPIO_INIT_FAILED,
    ERROR_USB_INIT_FAILED,
    ERROR_TASK_CREATE_FAILED,
    ERROR_MUTEX_CREATE_FAILED,
    ERROR_QUEUE_CREATE_FAILED,
    ERROR_AT_COMMAND_TIMEOUT,
    ERROR_DEBUG_LOGIN_FAILED,
    ERROR_USB_UPDATE_FAILED,
    ERROR_UBOOT_OPERATION_FAILED,
    ERROR_SYSTEM_STATE_INVALID,
    ERROR_MEMORY_ALLOCATION_FAILED,
    ERROR_FILE_NOT_FOUND,
    ERROR_DEVICE_NOT_CONNECTED,
    ERROR_UNKNOWN
} error_code_t;

// 错误信息结构体
typedef struct {
    error_code_t code;
    char message[256];
    char function[64];
    int line;
    uint32_t timestamp;
} error_info_t;

/**
 * @brief 初始化错误处理模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t error_handler_init(void);

/**
 * @brief 记录错误信息
 * @param code 错误代码
 * @param message 错误消息
 * @param function 发生错误的函数名
 * @param line 发生错误的行号
 */
void error_handler_log_error(error_code_t code, const char* message, 
                            const char* function, int line);

/**
 * @brief 获取最后一次错误信息
 * @param error_info 输出参数：错误信息结构体
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t error_handler_get_last_error(error_info_t* error_info);

/**
 * @brief 获取错误代码对应的字符串
 * @param code 错误代码
 * @return 错误代码字符串
 */
const char* error_handler_get_error_string(error_code_t code);

/**
 * @brief 清除错误记录
 */
void error_handler_clear_errors(void);

/**
 * @brief 获取错误统计信息
 * @param total_errors 输出参数：总错误数
 * @param last_error_time 输出参数：最后一次错误时间
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t error_handler_get_statistics(uint32_t* total_errors, uint32_t* last_error_time);

/**
 * @brief 系统恢复处理
 * 根据错误类型执行相应的恢复操作
 * @param code 错误代码
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t error_handler_system_recovery(error_code_t code);

/**
 * @brief 检查系统健康状态
 * @return true 系统健康，false 系统异常
 */
bool error_handler_check_system_health(void);

/**
 * @brief 错误处理任务
 * @param pvParameters 任务参数
 */
void error_handler_task(void* pvParameters);

/**
 * @brief 启动错误处理任务
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t error_handler_start_task(void);

/**
 * @brief 停止错误处理任务
 */
void error_handler_stop_task(void);

/**
 * @brief 去初始化错误处理模块
 */
void error_handler_deinit(void);

// 便利宏定义
#define LOG_ERROR(code, message) \
    error_handler_log_error(code, message, __FUNCTION__, __LINE__)

#define CHECK_ERROR_AND_RETURN(condition, code, message) \
    do { \
        if (!(condition)) { \
            LOG_ERROR(code, message); \
            return ESP_FAIL; \
        } \
    } while(0)

#define CHECK_ERROR_AND_GOTO(condition, code, message, label) \
    do { \
        if (!(condition)) { \
            LOG_ERROR(code, message); \
            goto label; \
        } \
    } while(0)

#endif // ERROR_HANDLER_H
