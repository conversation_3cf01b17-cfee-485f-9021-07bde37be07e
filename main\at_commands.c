#include "at_commands.h"
#include "state_machine.h"
#include "gpio_monitor.h"
#include "uboot_operations.h"
#include "usb_otg.h"
#include "esp_log.h"
#include "string.h"

static const char *TAG = "AT_COMMANDS";

// AT指令字符串映射表
static const struct {
    const char* cmd_str;
    at_command_type_t cmd_type;
} at_command_map[] = {
    {"<AT_STAR>", AT_CMD_STAR},
    {"<AT_TEST_DEBUG>", AT_CMD_TEST_DEBUG},
    {"<AT_GET_GPIO_STA>", AT_CMD_GET_GPIO_STA},
    {"<AT_TEST_USB_UPDATE>", AT_CMD_TEST_USB_UPDATE},
    {"<AT_GET_TEST_USB_UPDATE_STA>", AT_CMD_GET_TEST_USB_UPDATE_STA}
};

/**
 * @brief 初始化AT指令处理模块
 */
esp_err_t at_commands_init(void)
{
    ESP_LOGI(TAG, "Initializing AT commands module...");
    
    // 创建AT指令队列
    g_system_ctx.at_command_queue = xQueueCreate(10, sizeof(at_command_t));
    if (g_system_ctx.at_command_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create AT command queue");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "AT commands module initialized");
    return ESP_OK;
}

/**
 * @brief 解析AT指令字符串
 */
esp_err_t at_commands_parse(const char* cmd_str, at_command_t* cmd)
{
    if (cmd_str == NULL || cmd == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // 初始化命令结构体
    memset(cmd, 0, sizeof(at_command_t));
    cmd->type = AT_CMD_UNKNOWN;
    cmd->timestamp = xTaskGetTickCount();
    strncpy(cmd->raw_command, cmd_str, AT_CMD_MAX_LEN - 1);
    
    // 查找匹配的AT指令
    for (int i = 0; i < sizeof(at_command_map) / sizeof(at_command_map[0]); i++) {
        if (strstr(cmd_str, at_command_map[i].cmd_str) != NULL) {
            cmd->type = at_command_map[i].cmd_type;
            ESP_LOGI(TAG, "Parsed AT command: %s (type: %d)", 
                     at_command_map[i].cmd_str, cmd->type);
            return ESP_OK;
        }
    }
    
    ESP_LOGW(TAG, "Unknown AT command: %s", cmd_str);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 处理AT指令
 */
esp_err_t at_commands_process(at_command_t* cmd)
{
    if (cmd == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    esp_err_t ret = ESP_OK;
    
    ESP_LOGI(TAG, "Processing AT command type: %d", cmd->type);
    
    // 首先通过状态机检查指令是否可以在当前状态下执行
    ret = state_machine_process_event(cmd->type);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "State machine rejected command type: %d", cmd->type);
        return ret;
    }
    
    // 根据指令类型调用相应的处理函数
    switch (cmd->type) {
        case AT_CMD_STAR:
            ret = at_cmd_handle_star(cmd);
            break;
            
        case AT_CMD_TEST_DEBUG:
            ret = at_cmd_handle_test_debug(cmd);
            break;
            
        case AT_CMD_GET_GPIO_STA:
            ret = at_cmd_handle_get_gpio_sta(cmd);
            break;
            
        case AT_CMD_TEST_USB_UPDATE:
            ret = at_cmd_handle_test_usb_update(cmd);
            break;
            
        case AT_CMD_GET_TEST_USB_UPDATE_STA:
            ret = at_cmd_handle_get_usb_update_sta(cmd);
            break;
            
        default:
            ESP_LOGE(TAG, "Unsupported AT command type: %d", cmd->type);
            ret = ESP_ERR_NOT_SUPPORTED;
            break;
    }
    
    cmd->processed = true;
    return ret;
}

/**
 * @brief 发送AT指令响应
 */
esp_err_t at_commands_send_response(const char* response)
{
    if (response == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // 通过主控制串口发送响应
    int len = strlen(response);
    int sent = uart_write_bytes(UART_MAIN_CONTROL, response, len);
    
    if (sent != len) {
        ESP_LOGE(TAG, "Failed to send complete response, sent %d/%d bytes", sent, len);
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Sent AT response: %s", response);
    return ESP_OK;
}

/**
 * @brief 处理AT_STAR指令
 */
esp_err_t at_cmd_handle_star(at_command_t* cmd)
{
    ESP_LOGI(TAG, "Handling AT_STAR command");
    
    // 设置响应
    strncpy(cmd->response, AT_RESPONSE_STAR_OK, AT_RESPONSE_MAX_LEN - 1);
    
    // 发送响应
    esp_err_t ret = at_commands_send_response(cmd->response);
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "System activated successfully");
    }
    
    return ret;
}

/**
 * @brief 处理AT_TEST_DEBUG指令
 */
esp_err_t at_cmd_handle_test_debug(at_command_t* cmd)
{
    ESP_LOGI(TAG, "Handling AT_TEST_DEBUG command");
    
    // 调用U-Boot操作模块进行Debug串口登录测试
    esp_err_t ret = uboot_test_debug_login();
    
    if (ret == ESP_OK) {
        strncpy(cmd->response, AT_RESPONSE_DEBUG_OK, AT_RESPONSE_MAX_LEN - 1);
        ESP_LOGI(TAG, "Debug login test successful");
    } else {
        strncpy(cmd->response, AT_RESPONSE_DEBUG_ERROR, AT_RESPONSE_MAX_LEN - 1);
        ESP_LOGE(TAG, "Debug login test failed");
    }
    
    // 发送响应
    return at_commands_send_response(cmd->response);
}

/**
 * @brief 处理AT_GET_GPIO_STA指令
 */
esp_err_t at_cmd_handle_get_gpio_sta(at_command_t* cmd)
{
    ESP_LOGI(TAG, "Handling AT_GET_GPIO_STA command");
    
    // 获取GPIO状态
    uint8_t gpio_status = gpio_monitor_get_status();
    
    // 构造响应字符串：<AT_GET_GPIO_XXXXXXXX>
    // X为8位二进制GPIO状态，GPIO35对应最高位
    snprintf(cmd->response, AT_RESPONSE_MAX_LEN, 
             "%s%02X%02X%02X%02X%02X%02X%02X%02X>",
             AT_RESPONSE_GPIO_PREFIX,
             (gpio_status >> 7) & 1, (gpio_status >> 6) & 1,
             (gpio_status >> 5) & 1, (gpio_status >> 4) & 1,
             (gpio_status >> 3) & 1, (gpio_status >> 2) & 1,
             (gpio_status >> 1) & 1, (gpio_status >> 0) & 1);
    
    ESP_LOGI(TAG, "GPIO status: 0x%02X", gpio_status);
    
    // 发送响应
    return at_commands_send_response(cmd->response);
}

/**
 * @brief 处理AT_TEST_USB_UPDATE指令
 */
esp_err_t at_cmd_handle_test_usb_update(at_command_t* cmd)
{
    ESP_LOGI(TAG, "Handling AT_TEST_USB_UPDATE command");
    
    // 启动USB更新测试（异步执行）
    esp_err_t ret = uboot_start_usb_update_test();
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "USB update test started");
        // 不立即发送响应，等待AT_GET_TEST_USB_UPDATE_STA查询
    } else {
        ESP_LOGE(TAG, "Failed to start USB update test");
    }
    
    return ret;
}

/**
 * @brief 处理AT_GET_TEST_USB_UPDATE_STA指令
 */
esp_err_t at_cmd_handle_get_usb_update_sta(at_command_t* cmd)
{
    ESP_LOGI(TAG, "Handling AT_GET_TEST_USB_UPDATE_STA command");
    
    // 获取USB更新测试状态
    usb_update_status_t status = usb_otg_get_update_status();
    
    switch (status) {
        case USB_UPDATE_SUCCESS:
            strncpy(cmd->response, AT_RESPONSE_USB_UPDATE_OK, AT_RESPONSE_MAX_LEN - 1);
            ESP_LOGI(TAG, "USB update test completed successfully");
            break;
            
        case USB_UPDATE_ERROR:
            strncpy(cmd->response, AT_RESPONSE_USB_UPDATE_ERROR, AT_RESPONSE_MAX_LEN - 1);
            ESP_LOGE(TAG, "USB update test failed");
            break;
            
        case USB_UPDATE_IN_PROGRESS:
            // 测试仍在进行中，暂不发送响应
            ESP_LOGI(TAG, "USB update test still in progress");
            return ESP_ERR_NOT_FINISHED;
            
        default:
            strncpy(cmd->response, AT_RESPONSE_USB_UPDATE_ERROR, AT_RESPONSE_MAX_LEN - 1);
            ESP_LOGW(TAG, "Unknown USB update status: %d", status);
            break;
    }
    
    // 发送响应
    return at_commands_send_response(cmd->response);
}

/**
 * @brief 去初始化AT指令处理模块
 */
void at_commands_deinit(void)
{
    if (g_system_ctx.at_command_queue != NULL) {
        vQueueDelete(g_system_ctx.at_command_queue);
        g_system_ctx.at_command_queue = NULL;
    }
    
    ESP_LOGI(TAG, "AT commands module deinitialized");
}
