#!/usr/bin/env python3
"""
ESP32S3 看门狗超时修复验证脚本

这个脚本用于验证看门狗超时修复的效果，通过监控串口输出来检测：
1. 是否还有看门狗超时错误
2. 软件UART功能是否正常
3. 系统是否稳定运行
"""

import serial
import time
import re
import sys
import argparse
from datetime import datetime

class WatchdogTestMonitor:
    def __init__(self, port, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.test_start_time = None
        self.watchdog_errors = []
        self.uart_rx_count = 0
        self.system_restarts = 0
        
    def connect(self):
        """连接到ESP32S3设备"""
        try:
            self.serial_conn = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"✅ 已连接到 {self.port} (波特率: {self.baudrate})")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print("🔌 已断开连接")
    
    def parse_log_line(self, line):
        """解析日志行，检测关键事件"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 检测看门狗超时错误
        if "task_wdt: Task watchdog got triggered" in line:
            self.watchdog_errors.append({
                'time': timestamp,
                'line': line.strip()
            })
            print(f"🚨 [{timestamp}] 检测到看门狗超时: {line.strip()}")
            return 'watchdog_error'
        
        # 检测软件UART接收
        if "Software UART received byte" in line:
            self.uart_rx_count += 1
            if self.uart_rx_count % 10 == 0:  # 每10个字节报告一次
                print(f"📡 [{timestamp}] 软件UART已接收 {self.uart_rx_count} 字节")
            return 'uart_rx'
        
        # 检测系统重启
        if "ESP-ROM:esp32s3" in line or "Build:Mar 27 2021" in line:
            self.system_restarts += 1
            print(f"🔄 [{timestamp}] 检测到系统重启 (第{self.system_restarts}次)")
            return 'system_restart'
        
        # 检测任务启动
        if "task started" in line:
            print(f"🚀 [{timestamp}] 任务启动: {line.strip()}")
            return 'task_start'
        
        # 检测错误信息
        if " E (" in line and ("assert failed" in line or "panic" in line):
            print(f"💥 [{timestamp}] 系统错误: {line.strip()}")
            return 'system_error'
        
        return 'normal'
    
    def run_test(self, duration_minutes=10):
        """运行测试监控"""
        if not self.connect():
            return False
        
        self.test_start_time = time.time()
        end_time = self.test_start_time + (duration_minutes * 60)
        
        print(f"🧪 开始看门狗修复验证测试 (持续 {duration_minutes} 分钟)")
        print("=" * 60)
        
        try:
            while time.time() < end_time:
                if self.serial_conn.in_waiting > 0:
                    try:
                        line = self.serial_conn.readline().decode('utf-8', errors='ignore')
                        if line.strip():
                            self.parse_log_line(line)
                    except Exception as e:
                        print(f"⚠️  读取串口数据时出错: {e}")
                
                time.sleep(0.01)  # 10ms间隔
                
        except KeyboardInterrupt:
            print("\n⏹️  测试被用户中断")
        
        self.generate_report()
        return True
    
    def generate_report(self):
        """生成测试报告"""
        test_duration = time.time() - self.test_start_time
        
        print("\n" + "=" * 60)
        print("📊 看门狗修复验证测试报告")
        print("=" * 60)
        
        print(f"🕐 测试持续时间: {test_duration:.1f} 秒")
        print(f"🚨 看门狗超时错误: {len(self.watchdog_errors)} 次")
        print(f"📡 软件UART接收字节: {self.uart_rx_count} 个")
        print(f"🔄 系统重启次数: {self.system_restarts} 次")
        
        # 详细的看门狗错误报告
        if self.watchdog_errors:
            print("\n❌ 看门狗超时错误详情:")
            for i, error in enumerate(self.watchdog_errors, 1):
                print(f"  {i}. [{error['time']}] {error['line']}")
        else:
            print("\n✅ 未检测到看门狗超时错误 - 修复成功!")
        
        # 评估修复效果
        print("\n🎯 修复效果评估:")
        if len(self.watchdog_errors) == 0:
            print("  ✅ 看门狗超时问题已完全解决")
        elif len(self.watchdog_errors) < 3:
            print("  ⚠️  看门狗超时显著减少，但仍需进一步优化")
        else:
            print("  ❌ 看门狗超时问题仍然存在，需要进一步修复")
        
        if self.uart_rx_count > 0:
            print("  ✅ 软件UART功能正常工作")
        else:
            print("  ⚠️  未检测到软件UART活动")
        
        if self.system_restarts == 0:
            print("  ✅ 系统运行稳定，无异常重启")
        elif self.system_restarts <= 2:
            print("  ⚠️  系统偶有重启，需要关注")
        else:
            print("  ❌ 系统频繁重启，存在稳定性问题")
        
        # 总体评分
        score = 100
        score -= len(self.watchdog_errors) * 30  # 每个看门狗错误扣30分
        score -= self.system_restarts * 20       # 每次重启扣20分
        if self.uart_rx_count == 0:
            score -= 20                          # UART无活动扣20分
        
        score = max(0, score)  # 确保分数不为负
        
        print(f"\n🏆 总体评分: {score}/100")
        if score >= 90:
            print("  🎉 修复效果优秀!")
        elif score >= 70:
            print("  👍 修复效果良好")
        elif score >= 50:
            print("  🤔 修复效果一般，需要改进")
        else:
            print("  😞 修复效果不佳，需要重新检查")

def main():
    parser = argparse.ArgumentParser(description='ESP32S3 看门狗超时修复验证工具')
    parser.add_argument('port', help='串口端口 (例如: COM15 或 /dev/ttyUSB0)')
    parser.add_argument('-b', '--baudrate', type=int, default=115200, help='波特率 (默认: 115200)')
    parser.add_argument('-t', '--time', type=int, default=10, help='测试持续时间(分钟) (默认: 10)')
    
    args = parser.parse_args()
    
    monitor = WatchdogTestMonitor(args.port, args.baudrate)
    
    print("🐕 ESP32S3 看门狗超时修复验证工具")
    print(f"📍 串口: {args.port}")
    print(f"⚡ 波特率: {args.baudrate}")
    print(f"⏱️  测试时间: {args.time} 分钟")
    print()
    
    try:
        success = monitor.run_test(args.time)
        if not success:
            sys.exit(1)
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    main()
