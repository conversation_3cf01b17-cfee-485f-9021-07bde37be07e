#!/usr/bin/env python3
"""
ESP32S3项目编译验证脚本
用于验证修复后的代码能够正常编译
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(cmd, cwd=None, timeout=300):
    """运行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print(f"命令执行超时 ({timeout}秒)")
        return -1, "", "命令执行超时"
    except Exception as e:
        print(f"命令执行失败: {e}")
        return -1, "", str(e)

def check_esp_idf_environment():
    """检查ESP-IDF环境"""
    print("检查ESP-IDF环境...")
    
    # 检查IDF_PATH环境变量
    idf_path = os.environ.get('IDF_PATH')
    if not idf_path:
        print("❌ IDF_PATH环境变量未设置")
        return False
    
    print(f"✅ IDF_PATH: {idf_path}")
    
    # 检查idf.py命令
    ret_code, stdout, stderr = run_command("idf.py --version")
    if ret_code != 0:
        print("❌ idf.py命令不可用")
        print(f"错误: {stderr}")
        return False
    
    print(f"✅ ESP-IDF版本: {stdout.strip()}")
    return True

def clean_build():
    """清理构建目录"""
    print("清理构建目录...")
    
    if os.path.exists("build"):
        ret_code, stdout, stderr = run_command("idf.py fullclean")
        if ret_code != 0:
            print("❌ 清理构建目录失败")
            print(f"错误: {stderr}")
            return False
    
    print("✅ 构建目录已清理")
    return True

def configure_project():
    """配置项目"""
    print("配置项目...")
    
    ret_code, stdout, stderr = run_command("idf.py set-target esp32s3")
    if ret_code != 0:
        print("❌ 设置目标芯片失败")
        print(f"错误: {stderr}")
        return False
    
    print("✅ 目标芯片设置为ESP32S3")
    return True

def build_project():
    """构建项目"""
    print("开始构建项目...")
    start_time = time.time()
    
    ret_code, stdout, stderr = run_command("idf.py build", timeout=600)
    
    build_time = time.time() - start_time
    print(f"构建耗时: {build_time:.2f}秒")
    
    if ret_code != 0:
        print("❌ 项目构建失败")
        print("构建错误:")
        print(stderr)
        
        # 查找特定的编译错误
        if "implicit declaration of function" in stderr:
            print("\n🔍 检测到函数声明错误，可能的原因:")
            print("1. 缺少头文件包含")
            print("2. 函数名称在新版ESP-IDF中已更改")
            print("3. API兼容性问题")
        
        if "ets_delay_us" in stderr:
            print("\n🔍 检测到ets_delay_us函数错误:")
            print("在ESP-IDF v5.x中，ets_delay_us已更名为esp_rom_delay_us")
            print("请检查是否正确包含了esp_rom_sys.h头文件")
        
        return False
    
    print("✅ 项目构建成功")
    return True

def check_binary_files():
    """检查生成的二进制文件"""
    print("检查生成的二进制文件...")
    
    binary_files = [
        "build/GS068-01_test.bin",
        "build/GS068-01_test.elf",
        "build/bootloader/bootloader.bin"
    ]
    
    all_exist = True
    for file_path in binary_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def analyze_build_output():
    """分析构建输出"""
    print("分析构建输出...")
    
    # 检查编译警告
    log_file = "build/compile_commands.json"
    if os.path.exists(log_file):
        print(f"✅ 编译命令日志: {log_file}")
    
    # 检查内存使用情况
    map_file = "build/GS068-01_test.map"
    if os.path.exists(map_file):
        print(f"✅ 内存映射文件: {map_file}")
        
        # 简单分析内存使用
        try:
            with open(map_file, 'r') as f:
                content = f.read()
                if "Memory Configuration" in content:
                    print("✅ 内存配置正常")
        except Exception as e:
            print(f"⚠️  无法分析内存映射文件: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("ESP32S3项目编译验证脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("main"):
        print("❌ 当前目录不是ESP32项目根目录")
        print("请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查关键文件
    key_files = [
        "main/uart_passthrough.c",
        "main/esp_compat.h",
        "CMakeLists.txt",
        "sdkconfig"
    ]
    
    for file_path in key_files:
        if not os.path.exists(file_path):
            print(f"❌ 关键文件不存在: {file_path}")
            sys.exit(1)
        else:
            print(f"✅ 关键文件存在: {file_path}")
    
    # 执行验证步骤
    steps = [
        ("检查ESP-IDF环境", check_esp_idf_environment),
        ("清理构建目录", clean_build),
        ("配置项目", configure_project),
        ("构建项目", build_project),
        ("检查二进制文件", check_binary_files),
        ("分析构建输出", analyze_build_output),
    ]
    
    success_count = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
                break
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            break
    
    # 输出结果
    print(f"\n{'='*60}")
    print("验证结果:")
    print(f"完成步骤: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎉 所有验证步骤都成功完成！")
        print("项目编译修复成功，可以正常构建。")
        sys.exit(0)
    else:
        print("❌ 验证过程中出现错误")
        print("请检查上述错误信息并进行相应修复。")
        sys.exit(1)

if __name__ == "__main__":
    main()
