# ESP32S3 Linux嵌入式板测试系统使用指南

## 快速开始

### 1. 硬件连接

#### ESP32S3引脚连接
```
主控制串口 (接收AT指令):
- GPIO4 (TX) -> Linux板测试程序RX
- GPIO5 (RX) -> Linux板测试程序TX

Debug串口 (连接Linux板控制台):
- GPIO6 (TX) -> Linux板Debug串口RX
- GPIO7 (RX) -> Linux板Debug串口TX

透传串口1:
- GPIO15 (TX) -> 外部设备RX
- GPIO16 (RX) -> 外部设备TX

透传串口2 (软件实现):
- GPIO17 (TX) -> 外部设备RX
- GPIO18 (RX) -> 外部设备TX

GPIO监控 (连接Linux板GPIO输出):
- GPIO35 -> Linux板GPIO输出1
- GPIO36 -> Linux板GPIO输出2
- GPIO37 -> Linux板GPIO输出3
- GPIO38 -> Linux板GPIO输出4
- GPIO39 -> Linux板GPIO输出5
- GPIO40 -> Linux板GPIO输出6
- GPIO41 -> Linux板GPIO输出7
- GPIO42 -> Linux板GPIO输出8

USB-OTG (连接U盘):
- GPIO19 (D+)
- GPIO20 (D-)
```

### 2. 编译和烧录

```bash
# 设置ESP-IDF环境
. $HOME/esp/esp-idf/export.sh

# 进入项目目录
cd /path/to/GS068-01_test

# 配置项目 (可选)
idf.py menuconfig

# 编译项目
idf.py build

# 烧录到ESP32S3
idf.py -p /dev/ttyUSB0 flash

# 监控串口输出
idf.py -p /dev/ttyUSB0 monitor
```

### 3. 系统启动

系统启动后会显示：
```
I (xxx) MAIN: ESP32S3 Linux Board Test System Starting...
I (xxx) MAIN: Version: GS068-01_v1.0
I (xxx) MAIN: System startup completed successfully
I (xxx) MAIN: System is now in IDLE state, waiting for <AT_STAR> command...
```

## AT指令使用

### 系统激活
```
发送: <AT_STAR>
响应: <AT_STAR_OK>
说明: 激活系统，从IDLE状态切换到TESTING状态
```

### Debug串口登录测试
```
发送: <AT_TEST_DEBUG>
响应: <AT_TEST_DEBUG_OK> 或 <AT_TEST_DEBUG_ERROR>
说明: 测试Linux板Debug串口登录功能
执行时间: 最多30秒
```

### GPIO状态查询
```
发送: <AT_GET_GPIO_STA>
响应: <AT_GET_GPIO_XXXXXXXX>
说明: 获取8个GPIO的当前状态
格式: X为0或1，表示对应GPIO的电平状态
顺序: GPIO35(最高位) -> GPIO42(最低位)
示例: <AT_GET_GPIO_10110001> 表示GPIO35=1, GPIO36=0, GPIO37=1, GPIO38=1, GPIO39=0, GPIO40=0, GPIO41=0, GPIO42=1
```

### USB更新测试
```
发送: <AT_TEST_USB_UPDATE>
说明: 启动USB更新测试流程，该指令会立即返回，测试在后台执行

查询结果:
发送: <AT_GET_TEST_USB_UPDATE_STA>
响应: <AT_GET_TEST_USB_UPDATE_OK> 或 <AT_GET_TEST_USB_UPDATE_ERROR>
说明: 查询USB更新测试的执行结果
```

## 测试流程示例

### 基本功能测试
```bash
# 1. 激活系统
echo "<AT_STAR>" > /dev/ttyUSB0

# 2. 测试GPIO状态读取
echo "<AT_GET_GPIO_STA>" > /dev/ttyUSB0

# 3. 测试Debug登录
echo "<AT_TEST_DEBUG>" > /dev/ttyUSB0

# 4. 启动USB更新测试
echo "<AT_TEST_USB_UPDATE>" > /dev/ttyUSB0

# 5. 等待几秒后查询USB测试结果
sleep 10
echo "<AT_GET_TEST_USB_UPDATE_STA>" > /dev/ttyUSB0
```

### 串口透传测试
透传串口会自动将接收到的数据立即回传，无需特殊指令。

连接设备到GPIO15/16或GPIO17/18，发送数据即可看到回传效果。

## 系统监控

### 状态监控
系统每10秒会输出一次状态信息：
```
I (xxx) MAIN: System Status - State: TESTING, GPIO: 0xA5, USB: 0
```

### 错误监控
系统会自动记录和处理错误：
```
E (xxx) ERROR_HANDLER: ERROR[1]: UART Initialization Failed in init_uart_ports:45 - Failed to install main UART driver
```

### GPIO变化监控
GPIO状态变化时会输出日志：
```
I (xxx) GPIO_MONITOR: GPIO35 changed to 1 (change count: 5)
I (xxx) GPIO_MONITOR: GPIO status updated: 0xA5
```

## 故障排除

### 常见问题

1. **系统无响应**
   - 检查串口连接和波特率设置
   - 确认发送的AT指令格式正确
   - 查看ESP32S3的串口输出日志

2. **GPIO状态读取异常**
   - 检查GPIO35-42的硬件连接
   - 确认Linux板GPIO输出正常
   - 查看GPIO监控任务的日志输出

3. **Debug登录失败**
   - 检查UART1 (GPIO6/7) 与Linux板的连接
   - 确认Linux板Debug串口配置正确
   - 检查波特率是否匹配 (115200)

4. **USB更新测试失败**
   - 确认U盘已正确连接到ESP32S3
   - 检查U盘中是否包含目标文件 (Image, imx8mp-evk.dtb)
   - 查看USB监控任务的日志输出

### 调试方法

1. **启用详细日志**
   ```bash
   idf.py menuconfig
   # Component config -> Log output -> Default log verbosity -> Debug
   ```

2. **查看任务状态**
   系统会定期输出各任务的运行状态和统计信息

3. **错误历史查看**
   系统保存最近10次错误的详细信息，可通过日志查看

## 系统配置

### 修改GPIO引脚
编辑 `main/system_config.h` 中的引脚定义：
```c
// GPIO监控引脚定义
static const gpio_num_t gpio_monitor_pins[GPIO_MONITOR_COUNT] = {
    GPIO_NUM_35, GPIO_NUM_36, GPIO_NUM_37, GPIO_NUM_38,
    GPIO_NUM_39, GPIO_NUM_40, GPIO_NUM_41, GPIO_NUM_42
};
```

### 修改超时时间
编辑 `main/system_config.h` 中的超时定义：
```c
#define AT_COMMAND_TIMEOUT          30000   // 30秒
#define DEBUG_LOGIN_TIMEOUT         30000   // 30秒
#define UBOOT_COMMAND_TIMEOUT       10000   // 10秒
```

### 修改任务优先级
编辑 `main/system_config.h` 中的优先级定义：
```c
#define SYSTEM_TASK_PRIORITY        5
#define GPIO_TASK_PRIORITY          4
#define UART_TASK_PRIORITY          4
#define USB_TASK_PRIORITY           3
```

## 扩展开发

### 添加新的AT指令
1. 在 `at_commands.h` 中添加新的指令类型
2. 在 `at_commands.c` 中实现解析和处理函数
3. 更新指令映射表

### 添加新的测试模块
1. 创建新的 `.h/.c` 文件
2. 在 `main/CMakeLists.txt` 中添加源文件
3. 在 `main.c` 中初始化和启动新模块

### 修改硬件配置
根据实际硬件修改 `system_config.h` 中的引脚定义和配置参数。
