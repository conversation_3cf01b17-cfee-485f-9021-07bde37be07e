#ifndef UART_PASSTHROUGH_H
#define UART_PASSTHROUGH_H

#include "system_config.h"

/**
 * @brief 初始化串口透传模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uart_passthrough_init(void);

/**
 * @brief 初始化硬件UART
 * @param uart_num UART端口号
 * @param tx_pin TX引脚
 * @param rx_pin RX引脚
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uart_passthrough_init_hardware_uart(uart_port_t uart_num, gpio_num_t tx_pin, gpio_num_t rx_pin);

/**
 * @brief 初始化软件UART
 * @param tx_pin TX引脚
 * @param rx_pin RX引脚
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uart_passthrough_init_software_uart(gpio_num_t tx_pin, gpio_num_t rx_pin);

/**
 * @brief 硬件UART透传任务
 * @param pvParameters 任务参数，包含UART端口号
 */
void uart_passthrough_hardware_task(void* pvParameters);

/**
 * @brief 软件UART发送任务
 * @param pvParameters 任务参数
 */
void uart_passthrough_software_tx_task(void* pvParameters);

/**
 * @brief 软件UART接收任务
 * @param pvParameters 任务参数
 */
void uart_passthrough_software_rx_task(void* pvParameters);

/**
 * @brief 启动串口透传任务
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uart_passthrough_start_tasks(void);

/**
 * @brief 停止串口透传任务
 */
void uart_passthrough_stop_tasks(void);

/**
 * @brief 软件UART发送数据
 * @param data 要发送的数据
 * @param len 数据长度
 * @return 实际发送的字节数
 */
int uart_passthrough_software_send(const uint8_t* data, int len);

/**
 * @brief 软件UART接收数据
 * @param buffer 接收缓冲区
 * @param max_len 缓冲区最大长度
 * @param timeout_ms 超时时间（毫秒）
 * @return 实际接收的字节数
 */
int uart_passthrough_software_receive(uint8_t* buffer, int max_len, int timeout_ms);

/**
 * @brief 获取透传统计信息
 * @param uart_num UART端口号（UART_NUM_MAX表示软件UART）
 * @param tx_bytes 输出参数：发送字节数
 * @param rx_bytes 输出参数：接收字节数
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uart_passthrough_get_stats(uart_port_t uart_num, uint32_t* tx_bytes, uint32_t* rx_bytes);

/**
 * @brief 重置透传统计信息
 */
void uart_passthrough_reset_stats(void);

/**
 * @brief 获取软串口错误统计信息
 * @return 软串口接收错误次数
 */
uint32_t uart_passthrough_get_soft_uart_errors(void);

/**
 * @brief 去初始化串口透传模块
 */
void uart_passthrough_deinit(void);

#endif // UART_PASSTHROUGH_H
