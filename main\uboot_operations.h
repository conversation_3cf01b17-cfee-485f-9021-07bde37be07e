#ifndef UBOOT_OPERATIONS_H
#define UBOOT_OPERATIONS_H

#include "system_config.h"

/**
 * @brief 初始化U-Boot操作模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_operations_init(void);

/**
 * @brief 测试Debug串口登录
 * 通过UART1发送root\r\n，等待30秒检测返回数据是否包含#字符
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_test_debug_login(void);

/**
 * @brief 启动USB更新测试
 * 执行完整的U-Boot USB更新流程
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_start_usb_update_test(void);

/**
 * @brief 发送U-Boot命令并等待响应
 * @param command 要发送的命令
 * @param expected_response 期望的响应字符串（可为NULL）
 * @param timeout_ms 超时时间（毫秒）
 * @param response_buffer 响应缓冲区（可为NULL）
 * @param buffer_size 缓冲区大小
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_send_command(const char* command, const char* expected_response, 
                             int timeout_ms, char* response_buffer, int buffer_size);

/**
 * @brief 等待特定字符串出现
 * @param expected_string 期望的字符串
 * @param timeout_ms 超时时间（毫秒）
 * @param response_buffer 响应缓冲区（可为NULL）
 * @param buffer_size 缓冲区大小
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_wait_for_string(const char* expected_string, int timeout_ms,
                                char* response_buffer, int buffer_size);

/**
 * @brief 执行Linux登录流程
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_linux_login(void);

/**
 * @brief 执行系统重启
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_system_reboot(void);

/**
 * @brief 进入U-Boot命令行模式
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_enter_command_mode(void);

/**
 * @brief 启动UMS（USB Mass Storage）模式
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_start_ums_mode(void);

/**
 * @brief 退出UMS模式
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_exit_ums_mode(void);

/**
 * @brief 启动Linux系统
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_boot_linux(void);

/**
 * @brief 清空UART接收缓冲区
 * @param uart_num UART端口号
 */
void uboot_clear_uart_buffer(uart_port_t uart_num);

/**
 * @brief 发送Ctrl-C中断信号
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_send_ctrl_c(void);

/**
 * @brief 获取最后一次操作的错误信息
 * @param error_buffer 错误信息缓冲区
 * @param buffer_size 缓冲区大小
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t uboot_get_last_error(char* error_buffer, int buffer_size);

/**
 * @brief 去初始化U-Boot操作模块
 */
void uboot_operations_deinit(void);

#endif // UBOOT_OPERATIONS_H
