/**
 * @file soft_uart_test.c
 * @brief 基于Dedicated GPIO的软串口测试代码
 * 
 * 这个文件包含了测试新软串口实现的示例代码
 */

#include "soft_uart_test.h"
#include "uart_passthrough.h"
#include "system_config.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <inttypes.h>

static const char *TAG = "SOFT_UART_TEST";

/**
 * @brief 软串口性能测试
 */
void soft_uart_performance_test(void)
{
    ESP_LOGI(TAG, "Starting Dedicated GPIO Software UART performance test...");
    
    // 测试数据
    const char test_data[] = "Hello, Dedicated GPIO Software UART! 0123456789 ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    uint8_t rx_buffer[256];
    
    // 发送测试数据
    ESP_LOGI(TAG, "Sending test data: %s", test_data);
    int sent = uart_passthrough_software_send((const uint8_t*)test_data, strlen(test_data));
    ESP_LOGI(TAG, "Sent %d bytes", sent);
    
    // 等待一段时间让数据传输完成
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 接收数据
    int received = uart_passthrough_software_receive(rx_buffer, sizeof(rx_buffer) - 1, 1000);
    if (received > 0) {
        rx_buffer[received] = '\0';
        ESP_LOGI(TAG, "Received %d bytes: %s", received, (char*)rx_buffer);
    } else {
        ESP_LOGW(TAG, "No data received");
    }
    
    // 获取统计信息
    uint32_t tx_bytes, rx_bytes;
    if (uart_passthrough_get_stats(UART_NUM_MAX, &tx_bytes, &rx_bytes) == ESP_OK) {
        ESP_LOGI(TAG, "Statistics - TX: %" PRIu32 " bytes, RX: %" PRIu32 " bytes", tx_bytes, rx_bytes);
    }
    
    // 获取错误统计
    uint32_t errors = uart_passthrough_get_soft_uart_errors();
    ESP_LOGI(TAG, "RX errors: %" PRIu32, errors);
}

/**
 * @brief 软串口连续发送测试
 */
void soft_uart_continuous_test(int duration_seconds)
{
    ESP_LOGI(TAG, "Starting continuous test for %d seconds...", duration_seconds);
    
    const char test_pattern[] = "0123456789ABCDEF";
    uint64_t start_time = esp_timer_get_time();
    uint64_t end_time = start_time + (duration_seconds * 1000000ULL);
    
    int packet_count = 0;
    
    while (esp_timer_get_time() < end_time) {
        // 发送测试数据包
        char packet[32];
        snprintf(packet, sizeof(packet), "PKT%04d:%s\n", packet_count, test_pattern);
        
        int sent = uart_passthrough_software_send((const uint8_t*)packet, strlen(packet));
        if (sent == strlen(packet)) {
            packet_count++;
        } else {
            ESP_LOGW(TAG, "Failed to send complete packet %d", packet_count);
        }
        
        // 短暂延时
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    ESP_LOGI(TAG, "Continuous test completed. Sent %d packets", packet_count);
    
    // 最终统计
    uint32_t tx_bytes, rx_bytes;
    if (uart_passthrough_get_stats(UART_NUM_MAX, &tx_bytes, &rx_bytes) == ESP_OK) {
        ESP_LOGI(TAG, "Final statistics - TX: %" PRIu32 " bytes, RX: %" PRIu32 " bytes", tx_bytes, rx_bytes);
        ESP_LOGI(TAG, "Average throughput: %.2f bytes/sec", 
                 (float)tx_bytes / duration_seconds);
    }
    
    uint32_t errors = uart_passthrough_get_soft_uart_errors();
    ESP_LOGI(TAG, "Total RX errors: %" PRIu32, errors);
}

/**
 * @brief 软串口回环测试
 * 需要将TX和RX引脚短接
 */
void soft_uart_loopback_test(void)
{
    ESP_LOGI(TAG, "Starting loopback test (TX and RX pins should be connected)...");
    
    const char test_messages[][32] = {
        "Test message 1",
        "Hello World!",
        "0123456789",
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "Special chars: !@#$%^&*()"
    };
    
    int test_count = sizeof(test_messages) / sizeof(test_messages[0]);
    int passed = 0;
    
    for (int i = 0; i < test_count; i++) {
        ESP_LOGI(TAG, "Test %d: Sending '%s'", i + 1, test_messages[i]);
        
        // 发送数据
        int sent = uart_passthrough_software_send(
            (const uint8_t*)test_messages[i], 
            strlen(test_messages[i])
        );
        
        if (sent != strlen(test_messages[i])) {
            ESP_LOGE(TAG, "Test %d FAILED: Could not send complete message", i + 1);
            continue;
        }
        
        // 等待数据传输
        vTaskDelay(pdMS_TO_TICKS(50));
        
        // 接收数据
        uint8_t rx_buffer[64];
        int received = uart_passthrough_software_receive(
            rx_buffer, 
            sizeof(rx_buffer) - 1, 
            500
        );
        
        if (received <= 0) {
            ESP_LOGE(TAG, "Test %d FAILED: No data received", i + 1);
            continue;
        }
        
        rx_buffer[received] = '\0';
        
        // 比较数据
        if (strcmp((char*)rx_buffer, test_messages[i]) == 0) {
            ESP_LOGI(TAG, "Test %d PASSED: Received '%s'", i + 1, (char*)rx_buffer);
            passed++;
        } else {
            ESP_LOGE(TAG, "Test %d FAILED: Expected '%s', got '%s'", 
                     i + 1, test_messages[i], (char*)rx_buffer);
        }
        
        // 测试间隔
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    ESP_LOGI(TAG, "Loopback test completed: %d/%d tests passed", passed, test_count);
    
    if (passed == test_count) {
        ESP_LOGI(TAG, "All loopback tests PASSED!");
    } else {
        ESP_LOGW(TAG, "Some loopback tests FAILED. Check connections and timing.");
    }
}

/**
 * @brief 运行所有软串口测试
 */
void run_all_soft_uart_tests(void)
{
    ESP_LOGI(TAG, "=== Starting Dedicated GPIO Software UART Tests ===");
    
    // 重置统计信息
    uart_passthrough_reset_stats();
    
    // 基本性能测试
    ESP_LOGI(TAG, "\n--- Performance Test ---");
    soft_uart_performance_test();
    
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 回环测试
    ESP_LOGI(TAG, "\n--- Loopback Test ---");
    soft_uart_loopback_test();
    
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 连续测试
    ESP_LOGI(TAG, "\n--- Continuous Test ---");
    soft_uart_continuous_test(10); // 10秒连续测试
    
    ESP_LOGI(TAG, "=== All Software UART Tests Completed ===");
}
