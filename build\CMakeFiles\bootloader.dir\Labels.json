{"sources": [{"file": "E:/ESP_project/GS068-01_test/build/CMakeFiles/bootloader"}, {"file": "E:/ESP_project/GS068-01_test/build/CMakeFiles/bootloader.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/CMakeFiles/bootloader-complete.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "E:/ESP_project/GS068-01_test/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}