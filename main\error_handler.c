#include "error_handler.h"
#include "state_machine.h"
#include "esp_log.h"
#include "string.h"

static const char *TAG = "ERROR_HANDLER";

// 错误信息存储
#define MAX_ERROR_HISTORY 10
static error_info_t error_history[MAX_ERROR_HISTORY];
static int error_count = 0;
static int error_index = 0;
static SemaphoreHandle_t error_mutex = NULL;
static TaskHandle_t error_task_handle = NULL;

// 错误代码字符串映射
static const struct {
    error_code_t code;
    const char* string;
} error_code_strings[] = {
    {ERROR_NONE, "No Error"},
    {ERROR_UART_INIT_FAILED, "UART Initialization Failed"},
    {ERROR_GPIO_INIT_FAILED, "GPIO Initialization Failed"},
    {ERROR_USB_INIT_FAILED, "USB Initialization Failed"},
    {ERROR_TASK_CREATE_FAILED, "Task Creation Failed"},
    {ERROR_MUTEX_CREATE_FAILED, "Mutex Creation Failed"},
    {ERROR_QUEUE_CREATE_FAILED, "Queue Creation Failed"},
    {ERROR_AT_COMMAND_TIMEOUT, "AT Command Timeout"},
    {ERROR_DEBUG_LOGIN_FAILED, "Debug Login Failed"},
    {ERROR_USB_UPDATE_FAILED, "USB Update Failed"},
    {ERROR_UBOOT_OPERATION_FAILED, "U-Boot Operation Failed"},
    {ERROR_SYSTEM_STATE_INVALID, "Invalid System State"},
    {ERROR_MEMORY_ALLOCATION_FAILED, "Memory Allocation Failed"},
    {ERROR_FILE_NOT_FOUND, "File Not Found"},
    {ERROR_DEVICE_NOT_CONNECTED, "Device Not Connected"},
    {ERROR_UNKNOWN, "Unknown Error"}
};

/**
 * @brief 初始化错误处理模块
 */
esp_err_t error_handler_init(void)
{
    ESP_LOGI(TAG, "Initializing error handler module...");
    
    // 创建错误互斥锁
    error_mutex = xSemaphoreCreateMutex();
    if (error_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create error mutex");
        return ESP_FAIL;
    }
    
    // 初始化错误历史记录
    memset(error_history, 0, sizeof(error_history));
    error_count = 0;
    error_index = 0;
    
    ESP_LOGI(TAG, "Error handler module initialized");
    return ESP_OK;
}

/**
 * @brief 记录错误信息
 */
void error_handler_log_error(error_code_t code, const char* message, 
                            const char* function, int line)
{
    if (error_mutex == NULL) {
        ESP_LOGE(TAG, "Error handler not initialized");
        return;
    }
    
    if (xSemaphoreTake(error_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take error mutex");
        return;
    }
    
    // 记录错误信息
    error_info_t* error = &error_history[error_index];
    error->code = code;
    error->timestamp = xTaskGetTickCount() * portTICK_PERIOD_MS;
    error->line = line;
    
    if (message != NULL) {
        strncpy(error->message, message, sizeof(error->message) - 1);
        error->message[sizeof(error->message) - 1] = '\0';
    } else {
        strcpy(error->message, "No message");
    }
    
    if (function != NULL) {
        strncpy(error->function, function, sizeof(error->function) - 1);
        error->function[sizeof(error->function) - 1] = '\0';
    } else {
        strcpy(error->function, "Unknown");
    }
    
    // 更新索引和计数
    error_index = (error_index + 1) % MAX_ERROR_HISTORY;
    if (error_count < MAX_ERROR_HISTORY) {
        error_count++;
    }
    
    xSemaphoreGive(error_mutex);
    
    // 记录到ESP日志系统
    ESP_LOGE(TAG, "ERROR[%d]: %s in %s:%d - %s", 
             code, error_handler_get_error_string(code), function, line, message);
    
    // 触发系统恢复（如果需要）
    error_handler_system_recovery(code);
}

/**
 * @brief 获取最后一次错误信息
 */
esp_err_t error_handler_get_last_error(error_info_t* error_info)
{
    if (error_info == NULL || error_mutex == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(error_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return ESP_FAIL;
    }
    
    if (error_count == 0) {
        xSemaphoreGive(error_mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    // 获取最后一次错误（当前索引的前一个位置）
    int last_index = (error_index - 1 + MAX_ERROR_HISTORY) % MAX_ERROR_HISTORY;
    memcpy(error_info, &error_history[last_index], sizeof(error_info_t));
    
    xSemaphoreGive(error_mutex);
    return ESP_OK;
}

/**
 * @brief 获取错误代码对应的字符串
 */
const char* error_handler_get_error_string(error_code_t code)
{
    for (int i = 0; i < sizeof(error_code_strings) / sizeof(error_code_strings[0]); i++) {
        if (error_code_strings[i].code == code) {
            return error_code_strings[i].string;
        }
    }
    return "Unknown Error Code";
}

/**
 * @brief 清除错误记录
 */
void error_handler_clear_errors(void)
{
    if (error_mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(error_mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        memset(error_history, 0, sizeof(error_history));
        error_count = 0;
        error_index = 0;
        xSemaphoreGive(error_mutex);
        ESP_LOGI(TAG, "Error history cleared");
    }
}

/**
 * @brief 获取错误统计信息
 */
esp_err_t error_handler_get_statistics(uint32_t* total_errors, uint32_t* last_error_time)
{
    if (total_errors == NULL || last_error_time == NULL || error_mutex == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(error_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return ESP_FAIL;
    }
    
    *total_errors = error_count;
    
    if (error_count > 0) {
        int last_index = (error_index - 1 + MAX_ERROR_HISTORY) % MAX_ERROR_HISTORY;
        *last_error_time = error_history[last_index].timestamp;
    } else {
        *last_error_time = 0;
    }
    
    xSemaphoreGive(error_mutex);
    return ESP_OK;
}

/**
 * @brief 系统恢复处理
 */
esp_err_t error_handler_system_recovery(error_code_t code)
{
    ESP_LOGI(TAG, "Attempting system recovery for error code: %d", code);
    
    switch (code) {
        case ERROR_AT_COMMAND_TIMEOUT:
            // AT指令超时，可能需要重置状态机
            ESP_LOGW(TAG, "AT command timeout, considering state reset");
            break;
            
        case ERROR_DEBUG_LOGIN_FAILED:
            // Debug登录失败，可能需要重试
            ESP_LOGW(TAG, "Debug login failed, may need retry");
            break;
            
        case ERROR_USB_UPDATE_FAILED:
            // USB更新失败，重置USB状态
            ESP_LOGW(TAG, "USB update failed, resetting USB status");
            break;
            
        case ERROR_SYSTEM_STATE_INVALID:
            // 系统状态无效，尝试重置到IDLE状态
            ESP_LOGW(TAG, "Invalid system state, attempting reset to IDLE");
            state_machine_set_state(SYSTEM_STATE_IDLE);
            break;
            
        default:
            ESP_LOGD(TAG, "No specific recovery action for error code: %d", code);
            break;
    }
    
    return ESP_OK;
}

/**
 * @brief 检查系统健康状态
 */
bool error_handler_check_system_health(void)
{
    uint32_t total_errors, last_error_time;
    
    if (error_handler_get_statistics(&total_errors, &last_error_time) != ESP_OK) {
        return false;
    }
    
    // 如果最近5分钟内错误数量超过10个，认为系统不健康
    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    if (total_errors > 10 && (current_time - last_error_time) < 300000) {
        ESP_LOGW(TAG, "System health check failed: too many recent errors");
        return false;
    }
    
    return true;
}

/**
 * @brief 错误处理任务
 */
void error_handler_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Error handler task started");

    TickType_t last_wake_time = xTaskGetTickCount();
    TickType_t check_period = pdMS_TO_TICKS(5000); // 5秒检查周期
    if (check_period == 0) {
        check_period = 1; // 至少1个tick
    }

    while (1) {
        // 定期检查系统健康状态
        bool is_healthy = error_handler_check_system_health();
        if (!is_healthy) {
            ESP_LOGW(TAG, "System health check failed, may need intervention");

            // 可以在这里添加更多的恢复操作
            // 例如：重启某些模块、发送警告信息等
        }

        // 定期清理过期的错误记录（可选）
        static int cleanup_counter = 0;
        cleanup_counter++;
        if (cleanup_counter >= 120) { // 每10分钟清理一次
            ESP_LOGI(TAG, "Performing periodic error history cleanup");
            cleanup_counter = 0;

            // 这里可以添加清理逻辑，比如删除过期的错误记录
        }

        // 等待下一个检查周期
        vTaskDelayUntil(&last_wake_time, check_period);
    }
}

/**
 * @brief 启动错误处理任务
 */
esp_err_t error_handler_start_task(void)
{
    if (error_task_handle != NULL) {
        ESP_LOGW(TAG, "Error handler task already running");
        return ESP_OK;
    }

    BaseType_t ret = xTaskCreate(
        error_handler_task,
        "error_handler",
        SYSTEM_TASK_STACK_SIZE,
        NULL,
        SYSTEM_TASK_PRIORITY - 1, // 较低优先级
        &error_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create error handler task");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Error handler task started");
    return ESP_OK;
}

/**
 * @brief 停止错误处理任务
 */
void error_handler_stop_task(void)
{
    if (error_task_handle != NULL) {
        vTaskDelete(error_task_handle);
        error_task_handle = NULL;
        ESP_LOGI(TAG, "Error handler task stopped");
    }
}

/**
 * @brief 去初始化错误处理模块
 */
void error_handler_deinit(void)
{
    // 停止错误处理任务
    error_handler_stop_task();

    // 删除互斥锁
    if (error_mutex != NULL) {
        vSemaphoreDelete(error_mutex);
        error_mutex = NULL;
    }

    // 清空错误历史
    memset(error_history, 0, sizeof(error_history));
    error_count = 0;
    error_index = 0;

    ESP_LOGI(TAG, "Error handler module deinitialized");
}
