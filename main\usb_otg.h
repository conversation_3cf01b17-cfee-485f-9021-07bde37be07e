#ifndef USB_OTG_H
#define USB_OTG_H

#include "system_config.h"

/**
 * @brief 初始化USB-OTG模块
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_init(void);

/**
 * @brief 启动USB-OTG主机模式
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_start_host_mode(void);

/**
 * @brief 停止USB-OTG主机模式
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_stop_host_mode(void);

/**
 * @brief 检测U盘是否连接
 * @return true 已连接，false 未连接
 */
bool usb_otg_is_device_connected(void);

/**
 * @brief 挂载U盘FAT文件系统
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_mount_fat_filesystem(void);

/**
 * @brief 卸载U盘FAT文件系统
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_unmount_fat_filesystem(void);

/**
 * @brief 检查目标文件是否存在
 * @param filename 文件名
 * @return true 文件存在，false 文件不存在
 */
bool usb_otg_check_file_exists(const char* filename);

/**
 * @brief 检查所有目标文件是否存在
 * @return true 所有文件都存在，false 有文件不存在
 */
bool usb_otg_check_all_target_files(void);

/**
 * @brief 获取USB更新测试状态
 * @return USB更新测试状态
 */
usb_update_status_t usb_otg_get_update_status(void);

/**
 * @brief 设置USB更新测试状态
 * @param status 新的状态
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_set_update_status(usb_update_status_t status);

/**
 * @brief USB监控任务
 * @param pvParameters 任务参数
 */
void usb_otg_monitor_task(void* pvParameters);

/**
 * @brief 启动USB监控任务
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_start_monitor_task(void);

/**
 * @brief 停止USB监控任务
 */
void usb_otg_stop_monitor_task(void);

/**
 * @brief 执行USB文件检测
 * 在U-Boot UMS模式下检测目标文件
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_perform_file_detection(void);

/**
 * @brief 获取USB设备信息
 * @param device_info 输出参数：设备信息字符串
 * @param max_len 缓冲区最大长度
 * @return ESP_OK 成功，其他值失败
 */
esp_err_t usb_otg_get_device_info(char* device_info, int max_len);

/**
 * @brief 去初始化USB-OTG模块
 */
void usb_otg_deinit(void);

#endif // USB_OTG_H
