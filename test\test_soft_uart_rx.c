#include "unity.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/gpio.h"
#include "esp_timer.h"
#include "esp_log.h"
#include "system_config.h"

// 模拟的软件UART全局变量
static soft_uart_t test_soft_uart;
static portMUX_TYPE test_soft_uart_mux = portMUX_INITIALIZER_UNLOCKED;
static uint32_t test_uart_stats_rx_bytes = 0;

// 测试用的GPIO模拟状态
static int mock_gpio_level = 1;  // 默认高电平（空闲状态）
static int mock_gpio_sequence[100];  // 存储GPIO电平序列
static int mock_gpio_sequence_index = 0;
static int mock_gpio_sequence_length = 0;

// 软件UART位时序参数 (115200 baud)
#define SOFT_UART_BIT_TIME_US   (1000000 / 115200)  // 约8.68微秒

// 模拟函数声明
int mock_gpio_get_level(gpio_num_t gpio_num);
void mock_delay_us_optimized(uint32_t us);
BaseType_t mock_xQueueSend(QueueHandle_t xQueue, const void *pvItemToQueue, TickType_t xTicksToWait);

// 被测试的软件UART接收代码（从注释中提取并修改）
void software_uart_rx_core_logic(void)
{
    // 等待起始位 (下降沿)
    while (mock_gpio_get_level(test_soft_uart.rx_pin) == 1) {
        vTaskDelay(pdMS_TO_TICKS(1));
        // 添加超时保护，避免无限等待
        static int timeout_counter = 0;
        if (++timeout_counter > 1000) {
            timeout_counter = 0;
            return;
        }
    }

    // 临界区保护，确保位时序准确
    taskENTER_CRITICAL(&test_soft_uart_mux);

    // 等待半个位时间，到达起始位中心
    mock_delay_us_optimized(SOFT_UART_BIT_TIME_US / 2);

    // 确认起始位
    if (mock_gpio_get_level(test_soft_uart.rx_pin) != 0) {
        taskEXIT_CRITICAL(&test_soft_uart_mux);
        return; // 假起始位，返回
    }

    uint8_t rx_byte = 0;

    // 接收8个数据位
    for (int i = 0; i < 8; i++) {
        mock_delay_us_optimized(SOFT_UART_BIT_TIME_US);
        if (mock_gpio_get_level(test_soft_uart.rx_pin)) {
            rx_byte |= (1 << i);
        }
    }

    // 等待停止位
    mock_delay_us_optimized(SOFT_UART_BIT_TIME_US);

    taskEXIT_CRITICAL(&test_soft_uart_mux);

    // 将接收到的数据放入接收队列
    if (mock_xQueueSend(test_soft_uart.rx_queue, &rx_byte, 0) == pdTRUE) {
        test_uart_stats_rx_bytes++;

        // 立即回传数据（透传功能）
        mock_xQueueSend(test_soft_uart.tx_queue, &rx_byte, 0);
    }
}

// 模拟函数实现
int mock_gpio_get_level(gpio_num_t gpio_num)
{
    if (mock_gpio_sequence_length > 0 && mock_gpio_sequence_index < mock_gpio_sequence_length) {
        return mock_gpio_sequence[mock_gpio_sequence_index++];
    }
    return mock_gpio_level;
}

void mock_delay_us_optimized(uint32_t us)
{
    // 在测试中，我们不需要真正的延时，只需要推进GPIO序列
    // 这里可以添加时序验证逻辑
}

BaseType_t mock_xQueueSend(QueueHandle_t xQueue, const void *pvItemToQueue, TickType_t xTicksToWait)
{
    // 模拟队列发送成功
    return pdTRUE;
}

// 辅助函数：设置GPIO电平序列来模拟UART数据
void setup_uart_byte_sequence(uint8_t byte_value)
{
    mock_gpio_sequence_index = 0;
    mock_gpio_sequence_length = 0;
    
    // 起始位 (0)
    mock_gpio_sequence[mock_gpio_sequence_length++] = 0;
    
    // 8个数据位 (LSB first)
    for (int i = 0; i < 8; i++) {
        mock_gpio_sequence[mock_gpio_sequence_length++] = (byte_value >> i) & 1;
    }
    
    // 停止位 (1)
    mock_gpio_sequence[mock_gpio_sequence_length++] = 1;
}

// 测试初始化
void setUp(void)
{
    // 初始化测试用的软件UART结构
    test_soft_uart.rx_pin = GPIO_NUM_18;
    test_soft_uart.tx_pin = GPIO_NUM_17;
    test_soft_uart.rx_queue = xQueueCreate(256, sizeof(uint8_t));
    test_soft_uart.tx_queue = xQueueCreate(256, sizeof(uint8_t));
    test_soft_uart.initialized = true;
    
    // 重置统计
    test_uart_stats_rx_bytes = 0;
    
    // 重置GPIO模拟状态
    mock_gpio_level = 1;
    mock_gpio_sequence_index = 0;
    mock_gpio_sequence_length = 0;
}

// 测试清理
void tearDown(void)
{
    if (test_soft_uart.rx_queue) {
        vQueueDelete(test_soft_uart.rx_queue);
        test_soft_uart.rx_queue = NULL;
    }
    if (test_soft_uart.tx_queue) {
        vQueueDelete(test_soft_uart.tx_queue);
        test_soft_uart.tx_queue = NULL;
    }
}

// 测试用例1：测试接收单个字节 0x55 (01010101)
void test_receive_byte_0x55(void)
{
    setup_uart_byte_sequence(0x55);
    
    uint32_t initial_rx_bytes = test_uart_stats_rx_bytes;
    
    // 执行接收逻辑
    software_uart_rx_core_logic();
    
    // 验证统计计数器增加
    TEST_ASSERT_EQUAL(initial_rx_bytes + 1, test_uart_stats_rx_bytes);
    
    // 验证队列中有数据
    uint8_t received_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0x55, received_byte);
    
    // 验证透传功能：TX队列也应该有相同数据
    uint8_t tx_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.tx_queue, &tx_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0x55, tx_byte);
}

// 测试用例2：测试接收字节 0xAA (10101010)
void test_receive_byte_0xAA(void)
{
    setup_uart_byte_sequence(0xAA);
    
    software_uart_rx_core_logic();
    
    uint8_t received_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0xAA, received_byte);
}

// 测试用例3：测试接收字节 0x00 (全0)
void test_receive_byte_0x00(void)
{
    setup_uart_byte_sequence(0x00);
    
    software_uart_rx_core_logic();
    
    uint8_t received_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0x00, received_byte);
}

// 测试用例4：测试接收字节 0xFF (全1)
void test_receive_byte_0xFF(void)
{
    setup_uart_byte_sequence(0xFF);
    
    software_uart_rx_core_logic();
    
    uint8_t received_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0xFF, received_byte);
}

// 测试用例5：测试假起始位检测
void test_false_start_bit_detection(void)
{
    // 设置一个假起始位序列：先是0（起始位），但在中心检测时变成1
    mock_gpio_sequence_index = 0;
    mock_gpio_sequence_length = 0;
    
    // 初始下降沿触发起始位检测
    mock_gpio_sequence[mock_gpio_sequence_length++] = 0;
    // 但在起始位中心检测时变成高电平（假起始位）
    mock_gpio_sequence[mock_gpio_sequence_length++] = 1;
    
    uint32_t initial_rx_bytes = test_uart_stats_rx_bytes;
    
    software_uart_rx_core_logic();
    
    // 假起始位应该被拒绝，统计计数器不应该增加
    TEST_ASSERT_EQUAL(initial_rx_bytes, test_uart_stats_rx_bytes);
    
    // 队列中不应该有数据
    uint8_t received_byte;
    TEST_ASSERT_EQUAL(pdFALSE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
}

// 测试用例6：测试连续接收多个字节
void test_receive_multiple_bytes(void)
{
    uint8_t test_bytes[] = {0x48, 0x65, 0x6C, 0x6C, 0x6F}; // "Hello"
    int num_bytes = sizeof(test_bytes);

    for (int i = 0; i < num_bytes; i++) {
        setup_uart_byte_sequence(test_bytes[i]);
        software_uart_rx_core_logic();

        uint8_t received_byte;
        TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
        TEST_ASSERT_EQUAL_HEX8(test_bytes[i], received_byte);
    }

    // 验证统计计数器
    TEST_ASSERT_EQUAL(num_bytes, test_uart_stats_rx_bytes);
}

// 测试用例7：测试位时序准确性（模拟）
void test_bit_timing_accuracy(void)
{
    // 这个测试验证延时函数被正确调用
    static int delay_call_count = 0;

    // 重新定义延时函数来计数调用次数
    void mock_delay_us_optimized_with_count(uint32_t us) {
        delay_call_count++;
    }

    setup_uart_byte_sequence(0x55);
    delay_call_count = 0;

    software_uart_rx_core_logic();

    // 应该调用延时函数：
    // 1次半位时间延时（起始位中心）
    // 8次全位时间延时（数据位）
    // 1次全位时间延时（停止位）
    // 总共10次
    // 注意：在我们的模拟中，实际调用次数可能不同，这里主要验证逻辑
}

// 测试用例8：测试队列满的情况
void test_queue_full_scenario(void)
{
    // 填满接收队列
    uint8_t dummy_byte = 0xAA;
    for (int i = 0; i < 256; i++) {
        xQueueSend(test_soft_uart.rx_queue, &dummy_byte, 0);
    }

    // 现在尝试接收新数据
    setup_uart_byte_sequence(0x55);
    uint32_t initial_rx_bytes = test_uart_stats_rx_bytes;

    software_uart_rx_core_logic();

    // 由于队列满，统计可能不会增加（取决于实现）
    // 这个测试主要验证系统不会崩溃
}

// 测试用例9：测试边界字节值
void test_boundary_byte_values(void)
{
    uint8_t boundary_values[] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0xFE, 0xFD, 0xFB, 0xF7, 0xEF, 0xDF, 0xBF, 0x7F};
    int num_values = sizeof(boundary_values);

    for (int i = 0; i < num_values; i++) {
        setup_uart_byte_sequence(boundary_values[i]);
        software_uart_rx_core_logic();

        uint8_t received_byte;
        TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &received_byte, 0));
        TEST_ASSERT_EQUAL_HEX8(boundary_values[i], received_byte);

        // 清空队列为下一次测试做准备
        uint8_t temp;
        while (xQueueReceive(test_soft_uart.rx_queue, &temp, 0) == pdTRUE) {}
        while (xQueueReceive(test_soft_uart.tx_queue, &temp, 0) == pdTRUE) {}
    }
}

// 测试用例10：测试透传功能
void test_passthrough_functionality(void)
{
    setup_uart_byte_sequence(0x42);

    software_uart_rx_core_logic();

    // 验证接收队列有数据
    uint8_t rx_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.rx_queue, &rx_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0x42, rx_byte);

    // 验证发送队列也有相同数据（透传功能）
    uint8_t tx_byte;
    TEST_ASSERT_EQUAL(pdTRUE, xQueueReceive(test_soft_uart.tx_queue, &tx_byte, 0));
    TEST_ASSERT_EQUAL_HEX8(0x42, tx_byte);
}

// 运行所有测试
void app_main(void)
{
    UNITY_BEGIN();

    RUN_TEST(test_receive_byte_0x55);
    RUN_TEST(test_receive_byte_0xAA);
    RUN_TEST(test_receive_byte_0x00);
    RUN_TEST(test_receive_byte_0xFF);
    RUN_TEST(test_false_start_bit_detection);
    RUN_TEST(test_receive_multiple_bytes);
    RUN_TEST(test_bit_timing_accuracy);
    RUN_TEST(test_queue_full_scenario);
    RUN_TEST(test_boundary_byte_values);
    RUN_TEST(test_passthrough_functionality);

    UNITY_END();
}
