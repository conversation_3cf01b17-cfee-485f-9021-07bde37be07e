---
type: "manual"
description: "Example description"
---
ESP-IDF 编译烧录并监控的指令如下：
```
e:\ESP\IDF_TOOLS_PATH\python_env\idf5.3_py3.11_env\Scripts\python.exe e:\ESP\v5.3.3\esp-idf\components\esptool_py\esptool\esptool.py -p COM15 -b 460800 --before default_reset --after hard_reset --chip esp32s3 write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 build/bootloader/bootloader.bin 0x10000 GS068-01_test.bin 0x8000 build/partition_table/partition-table.bin 
```