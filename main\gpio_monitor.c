#include "gpio_monitor.h"
#include "esp_log.h"

static const char *TAG = "GPIO_MONITOR";

// GPIO状态变化统计
static uint32_t gpio_change_count[GPIO_MONITOR_COUNT] = {0};
static uint8_t gpio_last_status[GPIO_MONITOR_COUNT] = {0};

/**
 * @brief 初始化GPIO监控模块
 */
esp_err_t gpio_monitor_init(void)
{
    ESP_LOGI(TAG, "Initializing GPIO monitor module...");
    
    // 创建GPIO互斥锁
    g_system_ctx.gpio_mutex = xSemaphoreCreateMutex();
    if (g_system_ctx.gpio_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create GPIO mutex");
        return ESP_FAIL;
    }
    
    // 配置GPIO引脚为输入模式，启用上拉电阻
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = 0,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_ENABLE,
    };
    
    // 设置所有监控引脚的位掩码
    for (int i = 0; i < GPIO_MONITOR_COUNT; i++) {
        io_conf.pin_bit_mask |= (1ULL << gpio_monitor_pins[i]);
    }
    
    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure GPIO pins: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化GPIO状态
    g_system_ctx.gpio_status = 0;
    
    // 读取初始状态
    for (int i = 0; i < GPIO_MONITOR_COUNT; i++) {
        gpio_last_status[i] = gpio_get_level(gpio_monitor_pins[i]);
        if (gpio_last_status[i]) {
            g_system_ctx.gpio_status |= (1 << (GPIO_MONITOR_COUNT - 1 - i));
        }
        gpio_change_count[i] = 0;
    }
    
    ESP_LOGI(TAG, "GPIO monitor initialized, initial status: 0x%02X", g_system_ctx.gpio_status);
    
    // 打印引脚映射信息
    for (int i = 0; i < GPIO_MONITOR_COUNT; i++) {
        ESP_LOGI(TAG, "GPIO%d -> Bit%d (current: %d)", 
                 gpio_monitor_pins[i], GPIO_MONITOR_COUNT - 1 - i, gpio_last_status[i]);
    }
    
    return ESP_OK;
}

/**
 * @brief 获取当前GPIO状态
 */
uint8_t gpio_monitor_get_status(void)
{
    uint8_t status = 0;
    
    if (xSemaphoreTake(g_system_ctx.gpio_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        status = g_system_ctx.gpio_status;
        xSemaphoreGive(g_system_ctx.gpio_mutex);
    } else {
        ESP_LOGW(TAG, "Failed to take GPIO mutex, returning cached status");
        status = g_system_ctx.gpio_status;
    }
    
    return status;
}

/**
 * @brief 获取指定GPIO引脚的状态
 */
int gpio_monitor_get_pin_status(int pin_index)
{
    if (pin_index < 0 || pin_index >= GPIO_MONITOR_COUNT) {
        ESP_LOGE(TAG, "Invalid pin index: %d", pin_index);
        return -1;
    }
    
    return gpio_get_level(gpio_monitor_pins[pin_index]);
}

/**
 * @brief GPIO监控任务
 */
void gpio_monitor_task(void* pvParameters)
{
    ESP_LOGI(TAG, "GPIO monitor task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    TickType_t monitor_period = pdMS_TO_TICKS(10); // 10ms监控周期
    if (monitor_period == 0) {
        monitor_period = 1; // 至少1个tick
    }
    
    while (1) {
        uint8_t new_status = 0;
        bool status_changed = false;
        
        // 读取所有GPIO状态
        for (int i = 0; i < GPIO_MONITOR_COUNT; i++) {
            uint8_t current_level = gpio_get_level(gpio_monitor_pins[i]);
            
            // 检查状态变化
            if (current_level != gpio_last_status[i]) {
                gpio_change_count[i]++;
                gpio_last_status[i] = current_level;
                status_changed = true;
                
                ESP_LOGD(TAG, "GPIO%d changed to %d (change count: %lu)", 
                         gpio_monitor_pins[i], current_level, gpio_change_count[i]);
            }
            
            // 构建状态字节（GPIO35对应最高位）
            if (current_level) {
                new_status |= (1 << (GPIO_MONITOR_COUNT - 1 - i));
            }
        }
        
        // 更新全局状态
        if (xSemaphoreTake(g_system_ctx.gpio_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
            if (g_system_ctx.gpio_status != new_status) {
                g_system_ctx.gpio_status = new_status;
                status_changed = true;
            }
            xSemaphoreGive(g_system_ctx.gpio_mutex);
        }
        
        // 如果状态发生变化，记录日志
        if (status_changed) {
            ESP_LOGI(TAG, "GPIO status updated: 0x%02X", new_status);
        }
        
        // 等待下一个监控周期
        vTaskDelayUntil(&last_wake_time, monitor_period);
    }
}

/**
 * @brief 启动GPIO监控任务
 */
esp_err_t gpio_monitor_start_task(void)
{
    if (g_system_ctx.gpio_monitor_task_handle != NULL) {
        ESP_LOGW(TAG, "GPIO monitor task already running");
        return ESP_OK;
    }
    
    BaseType_t ret = xTaskCreate(
        gpio_monitor_task,
        "gpio_monitor",
        GPIO_TASK_STACK_SIZE,
        NULL,
        GPIO_TASK_PRIORITY,
        &g_system_ctx.gpio_monitor_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create GPIO monitor task");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "GPIO monitor task started");
    return ESP_OK;
}

/**
 * @brief 停止GPIO监控任务
 */
void gpio_monitor_stop_task(void)
{
    if (g_system_ctx.gpio_monitor_task_handle != NULL) {
        vTaskDelete(g_system_ctx.gpio_monitor_task_handle);
        g_system_ctx.gpio_monitor_task_handle = NULL;
        ESP_LOGI(TAG, "GPIO monitor task stopped");
    }
}

/**
 * @brief 获取GPIO状态变化统计信息
 */
esp_err_t gpio_monitor_get_change_count(int pin_index, uint32_t* change_count)
{
    if (pin_index < 0 || pin_index >= GPIO_MONITOR_COUNT || change_count == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    *change_count = gpio_change_count[pin_index];
    return ESP_OK;
}

/**
 * @brief 重置GPIO状态变化统计
 */
void gpio_monitor_reset_change_count(void)
{
    for (int i = 0; i < GPIO_MONITOR_COUNT; i++) {
        gpio_change_count[i] = 0;
    }
    ESP_LOGI(TAG, "GPIO change count reset");
}

/**
 * @brief 去初始化GPIO监控模块
 */
void gpio_monitor_deinit(void)
{
    // 停止监控任务
    gpio_monitor_stop_task();
    
    // 删除互斥锁
    if (g_system_ctx.gpio_mutex != NULL) {
        vSemaphoreDelete(g_system_ctx.gpio_mutex);
        g_system_ctx.gpio_mutex = NULL;
    }
    
    ESP_LOGI(TAG, "GPIO monitor module deinitialized");
}
