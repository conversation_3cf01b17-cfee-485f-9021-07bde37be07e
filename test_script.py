#!/usr/bin/env python3
"""
ESP32S3 Linux嵌入式板测试系统测试脚本
用于自动化测试AT指令功能
"""

import serial
import time
import sys
import argparse

class ESP32S3Tester:
    def __init__(self, port, baudrate=115200, timeout=5):
        """
        初始化测试器
        
        Args:
            port: 串口设备路径，如 /dev/ttyUSB0 或 COM3
            baudrate: 波特率，默认115200
            timeout: 串口超时时间，默认5秒
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial = None
        
    def connect(self):
        """连接到ESP32S3设备"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            print(f"已连接到 {self.port}，波特率 {self.baudrate}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("已断开连接")
    
    def send_command(self, command, expected_response=None, timeout=None):
        """
        发送AT指令并等待响应
        
        Args:
            command: AT指令字符串
            expected_response: 期望的响应字符串
            timeout: 超时时间，如果为None则使用默认超时
            
        Returns:
            tuple: (success, response) 成功标志和响应字符串
        """
        if not self.serial or not self.serial.is_open:
            return False, "串口未连接"
        
        try:
            # 发送指令
            self.serial.write(command.encode('utf-8'))
            self.serial.flush()
            print(f"发送指令: {command}")
            
            # 等待响应
            start_time = time.time()
            response = ""
            timeout_val = timeout if timeout else self.timeout
            
            while time.time() - start_time < timeout_val:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting).decode('utf-8', errors='ignore')
                    response += data
                    
                    # 如果指定了期望响应，检查是否匹配
                    if expected_response and expected_response in response:
                        print(f"收到期望响应: {response.strip()}")
                        return True, response.strip()
                
                time.sleep(0.1)
            
            print(f"收到响应: {response.strip()}")
            
            # 如果没有指定期望响应，只要有响应就算成功
            if not expected_response and response.strip():
                return True, response.strip()
            
            # 如果指定了期望响应但没有匹配，或者没有响应
            if expected_response:
                print(f"未收到期望响应 '{expected_response}'")
                return False, response.strip()
            else:
                print("未收到任何响应")
                return False, ""
                
        except Exception as e:
            print(f"发送指令失败: {e}")
            return False, str(e)
    
    def test_system_activation(self):
        """测试系统激活"""
        print("\n=== 测试系统激活 ===")
        success, response = self.send_command("<AT_STAR>", "<AT_STAR_OK>")
        if success:
            print("✓ 系统激活成功")
            return True
        else:
            print("✗ 系统激活失败")
            return False
    
    def test_gpio_status(self):
        """测试GPIO状态查询"""
        print("\n=== 测试GPIO状态查询 ===")
        success, response = self.send_command("<AT_GET_GPIO_STA>", "<AT_GET_GPIO_")
        if success:
            print("✓ GPIO状态查询成功")
            # 解析GPIO状态
            if "<AT_GET_GPIO_" in response:
                gpio_part = response.split("<AT_GET_GPIO_")[1].split(">")[0]
                if len(gpio_part) == 8:
                    print(f"GPIO状态: {gpio_part}")
                    for i, bit in enumerate(gpio_part):
                        gpio_num = 35 + i
                        print(f"  GPIO{gpio_num}: {bit}")
            return True
        else:
            print("✗ GPIO状态查询失败")
            return False
    
    def test_debug_login(self):
        """测试Debug登录"""
        print("\n=== 测试Debug登录 ===")
        print("注意: 此测试需要30秒超时时间")
        success, response = self.send_command("<AT_TEST_DEBUG>", None, timeout=35)
        
        if "<AT_TEST_DEBUG_OK>" in response:
            print("✓ Debug登录测试成功")
            return True
        elif "<AT_TEST_DEBUG_ERROR>" in response:
            print("✗ Debug登录测试失败")
            return False
        else:
            print("? Debug登录测试结果未知")
            return False
    
    def test_usb_update(self):
        """测试USB更新功能"""
        print("\n=== 测试USB更新功能 ===")
        
        # 启动USB更新测试
        print("启动USB更新测试...")
        success, response = self.send_command("<AT_TEST_USB_UPDATE>")
        if not success:
            print("✗ 启动USB更新测试失败")
            return False
        
        # 等待测试完成
        print("等待USB更新测试完成...")
        for i in range(12):  # 最多等待60秒
            time.sleep(5)
            print(f"查询测试状态... ({i+1}/12)")
            
            success, response = self.send_command("<AT_GET_TEST_USB_UPDATE_STA>")
            if success:
                if "<AT_GET_TEST_USB_UPDATE_OK>" in response:
                    print("✓ USB更新测试成功")
                    return True
                elif "<AT_GET_TEST_USB_UPDATE_ERROR>" in response:
                    print("✗ USB更新测试失败")
                    return False
        
        print("? USB更新测试超时")
        return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始ESP32S3系统功能测试")
        print("=" * 50)
        
        results = []
        
        # 系统激活测试
        results.append(("系统激活", self.test_system_activation()))
        
        # GPIO状态测试
        results.append(("GPIO状态查询", self.test_gpio_status()))
        
        # Debug登录测试
        results.append(("Debug登录", self.test_debug_login()))
        
        # USB更新测试
        results.append(("USB更新", self.test_usb_update()))
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("测试结果汇总:")
        success_count = 0
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  {test_name}: {status}")
            if result:
                success_count += 1
        
        print(f"\n总计: {success_count}/{len(results)} 项测试通过")
        
        return success_count == len(results)

def main():
    parser = argparse.ArgumentParser(description='ESP32S3测试系统自动化测试脚本')
    parser.add_argument('port', help='串口设备路径 (如: /dev/ttyUSB0 或 COM3)')
    parser.add_argument('-b', '--baudrate', type=int, default=115200, help='波特率 (默认: 115200)')
    parser.add_argument('-t', '--timeout', type=int, default=5, help='超时时间秒数 (默认: 5)')
    parser.add_argument('--test', choices=['activation', 'gpio', 'debug', 'usb', 'all'], 
                       default='all', help='要运行的测试 (默认: all)')
    
    args = parser.parse_args()
    
    # 创建测试器实例
    tester = ESP32S3Tester(args.port, args.baudrate, args.timeout)
    
    # 连接设备
    if not tester.connect():
        sys.exit(1)
    
    try:
        # 根据参数运行相应测试
        if args.test == 'activation':
            success = tester.test_system_activation()
        elif args.test == 'gpio':
            success = tester.test_gpio_status()
        elif args.test == 'debug':
            success = tester.test_debug_login()
        elif args.test == 'usb':
            success = tester.test_usb_update()
        else:  # all
            success = tester.run_all_tests()
        
        # 根据测试结果设置退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
