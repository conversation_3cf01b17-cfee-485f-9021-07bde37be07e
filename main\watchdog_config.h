#ifndef WATCHDOG_CONFIG_H
#define WATCHDOG_CONFIG_H

#include "esp_task_wdt.h"
#include "esp_log.h"

/**
 * @brief 看门狗配置和管理
 * 
 * 这个文件提供了任务看门狗的配置和管理功能，
 * 用于防止任务看门狗超时错误。
 */

// 看门狗配置宏
#define WDT_TIMEOUT_SECONDS         5       // 看门狗超时时间（秒）
#define WDT_RESET_INTERVAL_MS       1000    // 看门狗重置间隔（毫秒）

/**
 * @brief 安全地将当前任务添加到看门狗
 * @param task_name 任务名称（用于日志）
 * @return ESP_OK 成功，其他值失败
 */
static inline esp_err_t watchdog_add_current_task(const char* task_name)
{
    esp_err_t ret = esp_task_wdt_add(NULL);
    if (ret == ESP_OK) {
        ESP_LOGI("WDT", "Task '%s' added to watchdog", task_name ? task_name : "unknown");
    } else if (ret == ESP_ERR_INVALID_STATE) {
        ESP_LOGW("WDT", "Task '%s' already in watchdog", task_name ? task_name : "unknown");
        ret = ESP_OK; // 已经添加，不是错误
    } else {
        ESP_LOGE("WDT", "Failed to add task '%s' to watchdog: %s", 
                 task_name ? task_name : "unknown", esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief 安全地从看门狗移除当前任务
 * @param task_name 任务名称（用于日志）
 * @return ESP_OK 成功，其他值失败
 */
static inline esp_err_t watchdog_remove_current_task(const char* task_name)
{
    esp_err_t ret = esp_task_wdt_delete(NULL);
    if (ret == ESP_OK) {
        ESP_LOGI("WDT", "Task '%s' removed from watchdog", task_name ? task_name : "unknown");
    } else if (ret == ESP_ERR_NOT_FOUND) {
        ESP_LOGW("WDT", "Task '%s' not found in watchdog", task_name ? task_name : "unknown");
        ret = ESP_OK; // 没有找到，不是错误
    } else {
        ESP_LOGE("WDT", "Failed to remove task '%s' from watchdog: %s", 
                 task_name ? task_name : "unknown", esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief 安全地重置看门狗
 * @return ESP_OK 成功，其他值失败
 */
static inline esp_err_t watchdog_reset(void)
{
    esp_err_t ret = esp_task_wdt_reset();
    if (ret != ESP_OK && ret != ESP_ERR_NOT_FOUND) {
        ESP_LOGW("WDT", "Failed to reset watchdog: %s", esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief 检查看门狗状态
 * @return true 看门狗正常，false 看门狗异常
 */
static inline bool watchdog_is_healthy(void)
{
    // 尝试重置看门狗来检查状态
    esp_err_t ret = esp_task_wdt_reset();
    return (ret == ESP_OK || ret == ESP_ERR_NOT_FOUND);
}

/**
 * @brief 为长时间运行的循环提供看门狗友好的延时
 * @param delay_ms 延时时间（毫秒）
 * @param reset_wdt 是否重置看门狗
 */
static inline void watchdog_friendly_delay(uint32_t delay_ms, bool reset_wdt)
{
    if (delay_ms == 0) return;
    
    const uint32_t chunk_size = 100; // 每100ms重置一次看门狗
    uint32_t remaining = delay_ms;
    
    while (remaining > 0) {
        uint32_t current_delay = (remaining > chunk_size) ? chunk_size : remaining;
        vTaskDelay(pdMS_TO_TICKS(current_delay));
        
        if (reset_wdt) {
            watchdog_reset();
        }
        
        remaining -= current_delay;
    }
}

#endif // WATCHDOG_CONFIG_H
