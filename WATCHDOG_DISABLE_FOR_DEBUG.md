# 临时禁用看门狗功能以调试栈溢出问题

## 🎯 目的

临时禁用任务看门狗功能，以确定栈溢出问题是否与我们之前添加的看门狗管理代码有关，还是由其他原因引起的。

## 📝 禁用的看门狗功能

### 1. main/uart_passthrough.c - uart_passthrough_hardware_task 函数

**禁用的代码:**
```c
// 临时禁用看门狗以调试栈溢出问题
// 将任务添加到看门狗监控
// esp_err_t wdt_ret = watchdog_add_current_task(task_name);
// if (wdt_ret != ESP_OK) {
//     ESP_LOGW(TAG, "Failed to add %s task to watchdog, continuing anyway", task_name);
// }

// 临时禁用看门狗计数器以调试栈溢出问题
// uint32_t wdt_reset_counter = 0;
// const uint32_t wdt_reset_interval = 100; // 每100次循环重置一次看门狗

// 临时禁用定期看门狗重置
// 定期重置看门狗
// wdt_reset_counter++;
// if (wdt_reset_counter >= wdt_reset_interval) {
//     watchdog_reset();
//     wdt_reset_counter = 0;
// }

// 临时禁用接收数据后的看门狗重置
// 接收到数据后立即重置看门狗
// watchdog_reset();
```

### 2. main/uart_passthrough.c - uart_passthrough_software_rx_task 函数

**禁用的代码:**
```c
// 临时禁用看门狗以调试栈溢出问题
// 将任务添加到看门狗监控
// esp_err_t wdt_ret = watchdog_add_current_task("soft_uart_rx");
// if (wdt_ret != ESP_OK) {
//     ESP_LOGW(TAG, "Failed to add task to watchdog, continuing anyway");
// }

// 临时禁用看门狗计数器以调试栈溢出问题
// uint32_t wdt_reset_counter = 0;
// const uint32_t wdt_reset_interval = 100; // 每100次循环重置一次看门狗

// 临时禁用定期看门狗重置
// 定期重置看门狗
// wdt_reset_counter++;
// if (wdt_reset_counter >= wdt_reset_interval) {
//     watchdog_reset();
//     wdt_reset_counter = 0;
// }

// 临时禁用接收数据后的看门狗重置
// 接收到数据后立即重置看门狗
// watchdog_reset();

// 临时禁用看门狗友好延时，使用普通延时
// 长时间空闲，延长休眠时间并重置看门狗
// watchdog_friendly_delay(30, true); // 30ms看门狗友好休眠
vTaskDelay(pdMS_TO_TICKS(30)); // 临时使用普通延时
```

### 3. main/uboot_operations.c - uboot_wait_for_string 函数

**禁用的代码:**
```c
// 临时禁用看门狗计数器以调试栈溢出问题
// uint32_t wdt_reset_counter = 0;
// const uint32_t wdt_reset_interval = 10; // 每10次循环重置一次看门狗

// 临时禁用定期看门狗重置
// 定期重置看门狗，防止长时间等待导致超时
// wdt_reset_counter++;
// if (wdt_reset_counter >= wdt_reset_interval) {
//     watchdog_reset();
//     wdt_reset_counter = 0;
// }
```

### 4. main/main.c - at_commands_task 函数

**禁用的代码:**
```c
// 临时禁用看门狗以调试栈溢出问题
// 将任务添加到看门狗监控
// esp_err_t wdt_ret = watchdog_add_current_task("at_commands");
// if (wdt_ret != ESP_OK) {
//     ESP_LOGW(TAG, "Failed to add AT commands task to watchdog, continuing anyway");
// }

// 临时禁用看门狗计数器以调试栈溢出问题
// uint32_t wdt_reset_counter = 0;
// const uint32_t wdt_reset_interval = 50; // 每50次循环重置一次看门狗

// 临时禁用定期看门狗重置
// 定期重置看门狗
// wdt_reset_counter++;
// if (wdt_reset_counter >= wdt_reset_interval) {
//     watchdog_reset();
//     wdt_reset_counter = 0;
// }

// 临时禁用接收数据后的看门狗重置
// 接收到数据后立即重置看门狗
// watchdog_reset();
```

## 🔍 调试分析

### 预期结果

**如果栈溢出问题消失:**
- 说明栈溢出问题与看门狗管理代码有关
- 需要进一步优化看门狗代码的栈使用
- 可能需要调整看门狗管理策略

**如果栈溢出问题仍然存在:**
- 说明栈溢出问题与看门狗代码无关
- 需要查找其他原因（如其他新增代码、编译器优化等）
- 可能需要进一步增加栈大小或优化其他代码

### 测试步骤

1. **编译项目:**
   ```bash
   idf.py fullclean
   idf.py build
   ```

2. **烧录并监控:**
   ```bash
   idf.py -p COM15 flash monitor
   ```

3. **观察启动日志:**
   - 查看是否还有栈溢出错误
   - 确认UART任务是否正常启动
   - 监控系统稳定性

4. **功能测试:**
   - 测试UART透传功能
   - 测试AT指令处理
   - 测试U-Boot操作

## ⚠️ 注意事项

### 1. 看门狗超时风险

**禁用看门狗后可能出现的问题:**
- 任务可能会因为长时间运行而触发系统看门狗超时
- 如果出现死循环或阻塞，系统可能无法自动恢复
- 需要密切监控系统行为

### 2. 临时性措施

**这是临时调试措施:**
- 仅用于确定栈溢出的根本原因
- 一旦确定原因，需要重新启用看门狗功能
- 不应在生产环境中禁用看门狗

### 3. 系统稳定性

**监控要点:**
- 系统是否能正常启动
- 任务是否能正常运行
- 是否出现其他异常或错误

## 🔄 重新启用看门狗

**调试完成后，需要:**

1. **取消注释所有看门狗相关代码**
2. **根据调试结果优化看门狗管理策略**
3. **重新测试系统稳定性**

**重新启用的步骤:**
1. 将所有 `//` 注释的看门狗代码取消注释
2. 将 `vTaskDelay(pdMS_TO_TICKS(30))` 改回 `watchdog_friendly_delay(30, true)`
3. 重新编译和测试

## 📊 调试结果记录

**请在测试后记录以下信息:**

### 系统启动情况
- [ ] 系统正常启动
- [ ] 无栈溢出错误
- [ ] 所有任务正常创建

### 功能测试结果
- [ ] UART透传功能正常
- [ ] AT指令处理正常
- [ ] U-Boot操作正常

### 异常情况
- [ ] 是否出现看门狗超时
- [ ] 是否出现其他错误
- [ ] 系统稳定性如何

### 结论
- [ ] 栈溢出问题与看门狗代码有关
- [ ] 栈溢出问题与看门狗代码无关
- [ ] 需要进一步调试

## 📝 后续行动

**根据调试结果确定后续行动:**

1. **如果问题解决:** 优化看门狗代码，减少栈使用
2. **如果问题仍存在:** 查找其他原因，可能需要更大的栈空间
3. **重新启用看门狗:** 确保系统稳定性和可靠性
