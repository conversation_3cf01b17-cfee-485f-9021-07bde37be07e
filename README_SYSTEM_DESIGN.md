# ESP32S3 Linux嵌入式板测试系统设计文档

## 系统概述

本系统是基于ESP32S3开发的Linux嵌入式板测试程序，实现了完整的硬件连接拓扑、状态机管理、AT指令处理、多线程协调等功能。

## 硬件连接拓扑

### GPIO监控
- **GPIO35-42**：8个引脚连接到Linux板的GPIO输出，用于实时监控Linux板GPIO电平状态
- **监控方式**：实时读取，10ms监控周期
- **状态格式**：8位二进制，GPIO35对应最高位

### 串口连接
- **UART0 (GPIO4/5)**：主控制串口，接收Linux板测试程序的AT指令，波特率115200
- **UART1 (GPIO6/7)**：Debug串口，连接Linux板Debug控制台，波特率115200
- **UART2 (GPIO15/16)**：通用串口1，数据透传，波特率115200
- **软件UART (GPIO17/18)**：通用串口2，数据透传，波特率115200

### USB-OTG
- **GPIO19/20**：配置为USB-OTG主机模式，用于读取U盘FAT文件系统

## 系统架构

### 模块化设计
```
main/
├── system_config.h      # 系统配置和数据结构定义
├── main.c              # 主程序入口和系统初始化
├── state_machine.h/c   # 状态机模块
├── at_commands.h/c     # AT指令处理模块
├── gpio_monitor.h/c    # GPIO监控模块
├── uart_passthrough.h/c # 串口透传模块
├── usb_otg.h/c         # USB-OTG模块
├── uboot_operations.h/c # U-Boot操作封装
└── error_handler.h/c   # 错误处理和日志系统
```

### 状态机设计

#### 系统状态
- **IDLE**：初始状态，等待`<AT_STAR>`指令
- **TESTING**：测试状态，执行各种测试任务

#### 状态转换
- IDLE → TESTING：收到`<AT_STAR>`指令
- TESTING → TESTING：处理其他AT指令
- TESTING → IDLE：系统重置（可选）

### 多线程架构

#### 主要任务
1. **主任务**：AT指令处理和状态机管理
2. **GPIO监控任务**：实时读取GPIO35-42状态
3. **串口透传任务**：处理UART2/软件UART数据转发
4. **USB监控任务**：独立检测U盘文件状态
5. **错误处理任务**：系统健康监控和错误恢复

#### 任务优先级
- 主任务：优先级5
- GPIO监控：优先级4
- 串口透传：优先级4
- USB监控：优先级3
- 错误处理：优先级4

## AT指令规范

### `<AT_STAR>`
- **功能**：系统激活指令
- **响应**：`<AT_STAR_OK>`
- **状态转换**：IDLE → TESTING

### `<AT_TEST_DEBUG>`
- **功能**：测试Linux板Debug串口登录
- **执行流程**：
  1. 通过UART1发送`root\r\n`
  2. 等待30秒超时，检测返回数据是否包含`#`字符
- **响应**：`<AT_TEST_DEBUG_OK>` 或 `<AT_TEST_DEBUG_ERROR>`

### `<AT_GET_GPIO_STA>`
- **功能**：获取GPIO状态
- **响应格式**：`<AT_GET_GPIO_XXXXXXXX>`
- **说明**：X为8位二进制GPIO状态，GPIO35对应最高位

### `<AT_TEST_USB_UPDATE>`
- **功能**：测试U-Boot USB更新功能
- **执行流程**：
  1. UART1发送`root\r\n`，等待3秒
  2. 发送`reboot\r\n`
  3. 等待`Hit any key to stop autoboot:`，发送`\r\n`
  4. 等待`uboot=>`，发送`ums 0 mmc 2\r\n`
  5. 初始化USB-OTG，检测U盘中目标文件
  6. 发送Ctrl-C（0x03），等待`uboot=>`或1秒超时
  7. 发送`boot\r\n`

### `<AT_GET_TEST_USB_UPDATE_STA>`
- **功能**：查询USB更新测试结果
- **响应**：`<AT_GET_TEST_USB_UPDATE_OK>` 或 `<AT_GET_TEST_USB_UPDATE_ERROR>`

## 技术特性

### 错误处理机制
- **超时处理**：所有操作都有明确的超时时间
- **错误恢复**：根据错误类型执行相应的恢复操作
- **健康监控**：定期检查系统健康状态
- **错误记录**：保存最近10次错误的详细信息

### 日志系统
- **分级日志**：ERROR、WARN、INFO、DEBUG四个级别
- **模块标识**：每个模块都有独立的TAG标识
- **详细信息**：包含函数名、行号、时间戳等信息

### 资源管理
- **互斥锁保护**：所有共享资源都有互斥锁保护
- **队列通信**：任务间通过队列进行数据传递
- **内存管理**：合理的栈大小分配和内存使用

## 编译和使用

### 编译环境
- ESP-IDF v4.4或更高版本
- ESP32S3开发板
- 支持USB-OTG功能

### 编译命令
```bash
idf.py build
idf.py flash monitor
```

### 使用流程
1. 系统启动后处于IDLE状态
2. 发送`<AT_STAR>`激活系统
3. 系统进入TESTING状态，可以处理各种AT指令
4. 通过串口发送相应的AT指令进行测试

## 注意事项

### USB-OTG实现
当前USB-OTG模块提供了简化的实现框架，实际项目中需要：
1. 配置ESP-IDF的USB Host库
2. 实现FAT文件系统挂载
3. 添加USB设备检测和管理

### 软件UART
软件UART实现了基本的位时序控制，但可能需要根据实际应用调整：
1. 位时序精度
2. 中断处理
3. 错误检测

### 系统扩展
系统采用模块化设计，便于扩展：
1. 添加新的AT指令
2. 扩展GPIO监控功能
3. 增加新的测试模块

## 版本信息
- 版本：GS068-01_v1.0
- 开发平台：ESP32S3
- 开发框架：ESP-IDF
- 串口波特率：115200
- USB文件系统：FAT格式
